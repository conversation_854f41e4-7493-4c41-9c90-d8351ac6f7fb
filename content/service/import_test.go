package service

import (
	"context"
	"os"
	"testing"

	"hotel/common/utils"
	"hotel/content/domain"
	supplierDomain "hotel/supplier/domain"

	"github.com/stretchr/testify/assert"
)

func TestParseExpediaRowWithTSV(t *testing.T) {
	// 创建测试 TSV 文件
	testData := "hid\tname\tadr\tstar\tlati\tlongi\n"
	testData += "12345\tTest Expedia Hotel\t123 Test Street\t4\t40.7128\t-74.0060\n"
	testData += "67890\tAnother Expedia Hotel\t456 Another Street\t3\t34.0522\t-118.2437\n"

	tempFile, err := os.CreateTemp("", "test_expedia_*.tsv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 创建导入服务实例
	importSrv := &ImportService{}

	// 测试处理 TSV 文件
	var processedHotels []*domain.Hotel
	utils.ProcessTSVFile(tempFile.Name(), func(line int, row map[string]string) bool {
		if line > 1 { // 跳过标题行
			hotel, err := importSrv.parseExpediaRow(row)
			if err != nil {
				t.Errorf("parseExpediaRow failed: %v", err)
				return true
			}
			if hotel != nil {
				processedHotels = append(processedHotels, hotel)
			}
		}
		return true
	})

	// 验证结果
	assert.Len(t, processedHotels, 2)

	// 验证第一个酒店
	hotel1 := processedHotels[0]
	assert.NotZero(t, hotel1.ID) // ID 应该不为零
	assert.Equal(t, supplierDomain.Supplier_Expedia, hotel1.MasterSupplier)
	assert.Equal(t, "Test Expedia Hotel", hotel1.HotelStaticProfile.Name.En)
	assert.Equal(t, "123 Test Street", hotel1.HotelStaticProfile.Address.En)
	assert.Equal(t, 4.0, hotel1.HotelStaticProfile.RatingInfo.Rating)
	assert.Equal(t, 40.7128, hotel1.HotelStaticProfile.LatlngCoordinator.Google.Lat)
	assert.Equal(t, -74.0060, hotel1.HotelStaticProfile.LatlngCoordinator.Google.Lng)
	assert.Len(t, hotel1.HotelSupplierRef, 1)
	assert.Equal(t, supplierDomain.Supplier_Expedia, hotel1.HotelSupplierRef[0].Supplier)
	assert.Equal(t, "12345", hotel1.HotelSupplierRef[0].SupplierHotelID)

	// 验证第二个酒店
	hotel2 := processedHotels[1]
	assert.NotZero(t, hotel2.ID) // ID 应该不为零
	assert.Equal(t, supplierDomain.Supplier_Expedia, hotel2.MasterSupplier)
	assert.Equal(t, "Another Expedia Hotel", hotel2.HotelStaticProfile.Name.En)
	assert.Equal(t, "456 Another Street", hotel2.HotelStaticProfile.Address.En)
	assert.Equal(t, 3.0, hotel2.HotelStaticProfile.RatingInfo.Rating)
	assert.Equal(t, 34.0522, hotel2.HotelStaticProfile.LatlngCoordinator.Google.Lat)
	assert.Equal(t, -118.2437, hotel2.HotelStaticProfile.LatlngCoordinator.Google.Lng)
	assert.Len(t, hotel2.HotelSupplierRef, 1)
	assert.Equal(t, supplierDomain.Supplier_Expedia, hotel2.HotelSupplierRef[0].Supplier)
	assert.Equal(t, "67890", hotel2.HotelSupplierRef[0].SupplierHotelID)

	// 验证两个酒店的 ID 不同
	assert.NotEqual(t, hotel1.ID, hotel2.ID)
}

func TestImportLargeCSVToHotelWithExpediaTSV(t *testing.T) {
	// 创建测试 TSV 文件
	testData := "hid\tname\tadr\tstar\tlati\tlongi\n"
	testData += "12345\tTest Expedia Hotel\t123 Test Street\t4\t40.7128\t-74.0060\n"
	testData += "67890\tAnother Expedia Hotel\t456 Another Street\t3\t34.0522\t-118.2437\n"

	tempFile, err := os.CreateTemp("", "test_expedia_*.tsv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 创建导入服务实例（简化版本，不包含所有依赖）
	importSrv := &ImportService{}

	// 测试 importLargeCSVToHotel 方法
	ctx := context.Background()
	regions := map[string]string{}
	count, err := importSrv.importLargeCSVToHotel(ctx, regions, tempFile.Name(), 1000, 1, 10, supplierDomain.Supplier_Expedia)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, 2, count) // 应该处理了2个酒店
}
