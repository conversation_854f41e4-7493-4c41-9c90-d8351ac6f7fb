package utils

import (
	"fmt"

	"github.com/bytedance/sonic"
)

func ToLogString(v interface{}) string {
	if e, ok := v.(error); ok {
		return e.Error()
	}
	s, _ := sonic.MarshalString(v)
	return s
}

func ToJSON(v interface{}) string {
	s, _ := sonic.MarshalString(v)
	return s
}
func FromJSONP[T any](v string) *T {
	var t T
	err := sonic.UnmarshalString(v, &t)
	if err != nil {
		return nil
	}
	return &t
}
func FromJSON[T any](v string) T {
	var t T
	err := sonic.UnmarshalString(v, &t)
	if err != nil {
		return t
	}
	return t
}
func FromJSONE[T any](v string) (T, error) {
	var t T
	err := sonic.UnmarshalString(v, &t)
	if err != nil {
		return t, fmt.Errorf("FromJSONStringE failed(%v) from json(%s)", err, v)
	}
	return t, nil
}
