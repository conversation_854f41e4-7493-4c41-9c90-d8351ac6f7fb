# Trae Rules for Hotel-BE Project

## 项目结构规范

### 禁止在根目录直接创建文件和文件夹
- 所有新文件必须放在合适的模块目录中
- 根目录只允许存在项目配置文件（go.mod, go.sum, README.md, .gitignore 等）
- 临时文件、测试文件、示例文件必须放在相应的子目录中

### 文件组织规则
- **文档文件**: 放在 `docs/` 目录下，按类型分类
  - `docs/features/` - 功能文档
  - `docs/deployment/` - 部署文档
  - `docs/examples/` - 示例代码
  - `docs/api/` - API 文档
  - `docs/architecture/` - 架构文档

- **代码文件**: 按模块组织
  - `user/` - 用户管理模块
  - `trade/` - 交易模块
  - `search/` - 搜索模块
  - `geography/` - 地理模块
  - `content/` - 内容模块
  - `supplier/` - 供应商模块

- **配置和部署**: 放在相应目录
  - `deploy/` - 部署脚本
  - `cmd/` - 命令行工具
  - `api/` - API 服务

### 代码质量要求
- 遵循 Go 语言最佳实践
- 使用 TDD 开发模式
- 编写完整的单元测试和集成测试
- 保持代码简洁和可维护性

### 提交规范
- 提交前必须通过所有测试
- 提交信息要清晰描述变更内容
- 大文件变更需要特别说明

### 禁止行为
- 在根目录创建临时文件
- 在根目录创建测试文件
- 在根目录创建示例代码
- 在根目录创建文档文件
- 在根目录创建配置文件（除非是项目级配置）

### 例外情况
- 项目级配置文件（如 .gitignore, go.mod, go.sum）
- README.md 项目说明文件
- 构建配置文件
- 这些文件必须经过项目负责人同意才能创建或修改 