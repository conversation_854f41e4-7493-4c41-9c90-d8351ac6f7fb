package domain

import (
	"github.com/hashicorp/go-set/v3"
	common "hotel/common/protocol"
)

type BookRoom struct {
	RatePkgId string  `json:"ratePkgId"`
	Guests    []Guest `json:"guests"`
}

type BookReq struct {
	RatePkgId       string  `json:"ratePkgId" required:"true"` // got from HotelStaticDetail
	<PERSON>  `json:"booker,omitempty"`
	Guests          []Guest `json:"guests" required:"true"` // fill details for guests
	Payment         Payment `json:"payment"`
	PlatformOrderId int64   `json:"platformOrderId" apidoc:"HotelCode"`
	BookReqExtra
	common.Header `apidoc:"-"`
}

func (b BookReq) GetRoomCount() int64 {
	s := set.New[int64](len(b.Guests))
	for _, g := range b.Guests {
		s.Insert(g.RoomIndex)
	}
	return int64(s.<PERSON><PERSON>())
}

type BookReqExtra struct {
	RemarkToHotel string `json:"remarkToHotel"`
}

type Payment struct{}

type BookResp struct {
	SupplierOrderId string      `json:"supplierOrderId" required:"true"`
	OrderStatus     OrderStatus `json:"orderStatus" required:"true"`
}
