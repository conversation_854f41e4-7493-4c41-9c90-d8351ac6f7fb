package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"

	"hotel/common/bff"
	commonDomain "hotel/common/domain"
	"hotel/common/i18n"
	"hotel/common/log"
	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/common/utils"
	supplierDomain "hotel/supplier/domain"
	"hotel/user/domain"
	"hotel/user/protocol"
)

func (s *UserService) CreateEntity(ctx context.Context, req *protocol.CreateEntityReq) (*protocol.CreateEntityResp, error) {
	if err := s.dao.Entity.CreateEntity(ctx, req.Data); err != nil {
		return nil, err
	}
	return &protocol.CreateEntityResp{Data: req.Data}, nil
}
func (s *UserService) UpdateEntity(ctx context.Context, req *protocol.UpdateEntityReq) (*protocol.UpdateEntityResp, error) {
	existed, err := s.dao.Entity.Get(ctx, req.Data.ID)
	if err != nil {
		return nil, err
	}
	d := req.Data
	if d.Status >= 0 {
		existed.Status = d.Status
	}
	if !d.DistributionConfig.IsNil() {
		existed.DistributionConfig = d.DistributionConfig
	}
	if !d.Profile.IsNil() {
		existed.Profile = d.Profile
	}
	if !d.FinanceConfig.IsNil() {
		existed.FinanceConfig = d.FinanceConfig
	}
	if !d.SupplierConfig.IsNil() {
		existed.SupplierConfig = d.SupplierConfig
	}
	if err = s.dao.Entity.UpdateEntity(ctx, existed); err != nil {
		return nil, err
	}
	return &protocol.UpdateEntityResp{Data: existed}, nil
}

func (s *UserService) ListEntity(ctx context.Context, req *protocol.ListEntityReq) (*protocol.ListEntityResp, error) {
	var out []*domain.Entity
	var err error

	// 如果没有指定 parentEntityIds，则返回所有实体
	if len(req.ParentEntityIDs) == 0 {
		out, err = s.dao.Entity.FindAll(ctx)
	} else {
		out, err = s.dao.Entity.FindByRootIDs(ctx, req.ParentEntityIDs)
	}

	if err != nil {
		return nil, err
	}
	logx.WithContext(ctx).Infof("ListEntity dao out(%s)", utils.ToJSON(out))

	// 构建响应数据
	var ffEntities []*bff.ElementRow[domain.Entity]
	for _, entity := range out {
		// 构建实体类型显示 - 支持多种类型组合
		entityTypeText := buildEntityTypeI18N(entity.Type)

		// 构建状态显示
		statusText := i18n.I18N{Zh: "禁用", En: "Disabled", Ar: "معطل"}
		if entity.Status == commonDomain.StatusEnable {
			statusText = i18n.I18N{Zh: "启用", En: "Enabled", Ar: "مفعل"}
		}

		// 构建操作按钮
		actionButtons := []bff.Button{
			{
				ID: bff.ActionIdEdit,
				Content: bff.ElementItem{
					Type: bff.ElementTypeText,
					Content: &bff.ElementText{
						Text: i18n.I18N{
							Zh: "编辑",
							En: "Edit",
							Ar: "تحرير",
						},
					},
				},
				Hover: i18n.I18N{
					Zh: "编辑实体",
					En: "Edit Entity",
					Ar: "تحرير الكيان",
				},
				JumpURL: fmt.Sprintf("/entity/entity-edit/%d", entity.ID),
			},
			{
				ID: bff.ActionIdDelete,
				Content: bff.ElementItem{
					Type: bff.ElementTypeText,
					Content: &bff.ElementText{
						Text: i18n.I18N{
							Zh: "删除",
							En: "Delete",
							Ar: "حذف",
						},
					},
				},
				Hover: i18n.I18N{
					Zh: "删除实体",
					En: "Delete Entity",
					Ar: "حذف الكيان",
				},
			},
		}

		ffEntities = append(ffEntities, &bff.ElementRow[domain.Entity]{
			Raw: *entity,
			Key: entity.Name,
			Columns: []bff.ElementItem{
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: fmt.Sprintf("%d", entity.ID), Zh: fmt.Sprintf("%d", entity.ID), Ar: fmt.Sprintf("%d", entity.ID)},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: entity.Name, Zh: entity.Name, Ar: entity.Name},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: entityTypeText,
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: statusText,
					},
				},
				{
					Type: bff.ElementTypeTime,
					Content: &bff.ElementTime{
						Time: entity.CreateTime,
					},
				},
				{
					Type: bff.ElementTypeButtonGroup,
					Content: bff.ButtonGroupContent{
						Buttons: actionButtons,
					},
				},
			},
		})
	}

	return &protocol.ListEntityResp{
		bff.Table[domain.Entity]{
			HeaderKeys: []string{"ID", "实体名称", "类型", "状态", "创建时间", "操作"},
			Rows:       ffEntities,
			PageResp:   pagehelper.PageResp{Total: int64(len(out))},
		},
	}, nil
}

func (s *UserService) GetEntity(ctx context.Context, id types.ID) (*domain.Entity, error) {
	out, err := s.dao.Entity.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	log.Infoc(ctx, "GetEntity out:%s", utils.ToJSON(out))
	return out, nil
}

func (s *UserService) GetSupplierEntity(ctx context.Context, supplier supplierDomain.Supplier) (*domain.Entity, error) {
	return s.GetEntity(ctx, domain.SupplierToEntityId(supplier))
}

// buildEntityTypeI18N 构建实体类型的多语言显示文本，支持多种类型组合
func buildEntityTypeI18N(entityType domain.EntityType) i18n.I18N {
	var zhTypes, enTypes, arTypes []string

	if entityType.HasType(domain.EntityTypePlatform) {
		zhTypes = append(zhTypes, "平台")
		enTypes = append(enTypes, "Platform")
		arTypes = append(arTypes, "منصة")
	}
	if entityType.HasType(domain.EntityTypeTenant) {
		zhTypes = append(zhTypes, "租户")
		enTypes = append(enTypes, "Tenant")
		arTypes = append(arTypes, "مستأجر")
	}
	if entityType.HasType(domain.EntityTypeCustomer) {
		zhTypes = append(zhTypes, "客户")
		enTypes = append(enTypes, "Customer")
		arTypes = append(arTypes, "عميل")
	}
	if entityType.HasType(domain.EntityTypeExtranet) {
		zhTypes = append(zhTypes, "外网")
		enTypes = append(enTypes, "Extranet")
		arTypes = append(arTypes, "شبكة خارجية")
	}
	if entityType.HasType(domain.EntityTypeSupplier) {
		zhTypes = append(zhTypes, "供应商")
		enTypes = append(enTypes, "Supplier")
		arTypes = append(arTypes, "مورد")
	}

	if len(zhTypes) == 0 {
		return i18n.I18N{Zh: "未知", En: "Unknown", Ar: "غير معروف"}
	}

	return i18n.I18N{
		Zh: strings.Join(zhTypes, "|"),
		En: strings.Join(enTypes, "|"),
		Ar: strings.Join(arTypes, "|"),
	}
}
