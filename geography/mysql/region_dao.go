//go:generate go run ../../build/monitorgen/main.go
package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"hotel/common/config"
	"hotel/common/log"
	"hotel/common/types"
	"hotel/common/utils"
	"hotel/geography/domain"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type RegionDao struct {
	conn sqlx.SqlConn
	rm   *RegionModel
	db   *sql.DB
}

func NewRegionDao(conn sqlx.SqlConn) *RegionDao {
	db, err := conn.RawDB()
	if err != nil {
		panic(err)
	}
	return &RegionDao{conn: conn, db: db, rm: NewRegionModel(conn)}
}
func convertDomain2Model(in *domain.Region) *Region {
	return &Region{
		Id:                     in.ID.Int64(),
		Name:                   in.Name,
		Type:                   int64(in.Type),
		ExpediaId:              in.ExpediaId,
		CtripId:                in.TripId,
		DidaId:                 in.DidaId,
		NameFull:               in.NameFull,
		CountryCode:            in.CountryCode,
		CountrySubdivisionCode: in.CountrySubdivisionCode,
		Coordinates:            utils.ToJSON(in.Coordinates),
		Ancestors:              utils.ToJSON(in.Ancestors),
		Extra:                  utils.ToJSON(in.Extra),
		IsDeleted:              0,
	}
}

var (
	_d sync.Map
)

//mon:gen
func (r *RegionDao) LoadAllRegionsFromFile(ctx context.Context) ([]*domain.Region, error) {
	if v, ok := _d.Load("LoadAllRegionsFromFile"); ok && v != nil {
		return v.([]*domain.Region), nil
	}

	configDir := path.Join(config.GetProjectPath(), "geography/config")

	// Load from Ctrip supplier directory
	ctripRegions, err := r.loadCtripRegions(ctx, configDir)
	if err != nil {
		logx.WithContext(ctx).Errorf("Failed to load Ctrip regions: %v", err)
		ctripRegions = make([]*domain.Region, 0)
	} else {
		logx.WithContext(ctx).Infof("Loaded %d regions from Ctrip", len(ctripRegions))
	}

	// Load from Dida supplier directory
	didaRegions, err := r.loadDidaRegions(ctx, configDir)
	if err != nil {
		logx.WithContext(ctx).Errorf("Failed to load Dida regions: %v", err)
		didaRegions = make([]*domain.Region, 0)
	} else {
		logx.WithContext(ctx).Infof("Loaded %d regions from Dida", len(didaRegions))
	}

	// Merge regions from different suppliers
	mergedRegions := r.mergeRegionsFromSuppliers(ctx, ctripRegions, didaRegions)

	logx.WithContext(ctx).Infof("Total loaded %d regions after merging suppliers", len(mergedRegions))
	_d.Store("LoadAllRegionsFromFile", mergedRegions)
	return mergedRegions, nil
}

// loadCtripRegions loads regions from Ctrip supplier directory
func (r *RegionDao) loadCtripRegions(ctx context.Context, configDir string) ([]*domain.Region, error) {
	ctripDir := path.Join(configDir, "ctrip")
	files, err := ioutil.ReadDir(ctripDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read ctrip directory: %w", err)
	}

	regions := make([]*domain.Region, 0)
	countries := map[string]*domain.Region{}

	// First pass: load countries
	for _, f := range files {
		if f.IsDir() {
			continue
		}
		if f.Name() == "country.json" {
			fc := utils.ReadJSONArrayFile[domain.CtripCountryBaseInfo](filepath.Join(ctripDir, f.Name()))
			for _, l := range fc {
				country := l.ToRegion()
				countries[country.Extra.NameZh] = country
				regions = append(regions, country)
			}
			break
		}
	}

	// Second pass: load cities and POIs
	for _, f := range files {
		if f.IsDir() || f.Name() == "country.json" {
			continue
		}
		if strings.HasSuffix(f.Name(), ".json") && strings.HasPrefix(f.Name(), "country_") {
			fc := utils.ReadJSONArrayFile[domain.CtripPOIData](filepath.Join(ctripDir, f.Name()))
			// Extract country name from filename: country_中国_China.json -> 中国
			parts := strings.Split(f.Name(), "_")
			if len(parts) >= 2 {
				countryNameZh := parts[1]
				countryRegion := countries[countryNameZh]
				for _, l := range fc {
					region := l.ToRegion(countryRegion)
					regions = append(regions, region)
				}
			}
		}
	}

	return regions, nil
}

// loadDidaRegions loads regions from Dida supplier directory with multilingual support
func (r *RegionDao) loadDidaRegions(ctx context.Context, configDir string) ([]*domain.Region, error) {
	didaDir := path.Join(configDir, "dida")

	regions := make([]*domain.Region, 0)
	countries := map[string]*domain.Region{}

	// First pass: load countries with multilingual support
	countriesEn := r.loadDidaCountriesFile(ctx, didaDir, "countries_en-US.json")
	countriesZh := r.loadDidaCountriesFile(ctx, didaDir, "countries_zh-CN.json")

	// Merge English and Chinese country data
	for code, enCountry := range countriesEn {
		if zhCountry, exists := countriesZh[code]; exists {
			// Merge Chinese name into English region
			enCountry.Extra.NameZh = zhCountry.Name
		}
		countries[code] = enCountry
		regions = append(regions, enCountry)
	}

	logx.WithContext(ctx).Infof("Loaded %d countries with multilingual support", len(countries))

	// Second pass: load destinations with multilingual support
	destinationsByCountry := r.loadDidaDestinationsWithMultilingual(ctx, didaDir, countries)
	for _, destinations := range destinationsByCountry {
		regions = append(regions, destinations...)
		//log.Infoc(ctx, "Loaded %d destinations for country %s with multilingual support",
		//	len(destinations), countryCode)
	}

	return regions, nil
}

// loadDidaCountriesFile loads countries from a specific Dida countries file
func (r *RegionDao) loadDidaCountriesFile(ctx context.Context, didaDir, filename string) map[string]*domain.Region {
	countries := make(map[string]*domain.Region)

	filePath := filepath.Join(didaDir, filename)
	fileContent := utils.ReadFile(filePath)
	if fileContent == nil {
		logx.WithContext(ctx).Errorf("Failed to read Dida countries file %s", filename)
		return countries
	}

	var countriesFile domain.DidaCountriesFile
	if err := json.Unmarshal(fileContent, &countriesFile); err != nil {
		logx.WithContext(ctx).Errorf("Failed to unmarshal Dida countries file %s: %v", filename, err)
		return countries
	}

	for _, country := range countriesFile.Countries {
		region := country.ToRegion()
		countries[country.Code] = region
	}

	logx.WithContext(ctx).Infof("Loaded %d countries from %s", len(countriesFile.Countries), filename)
	return countries
}

// loadDidaDestinationsWithMultilingual loads destinations with both English and Chinese names
func (r *RegionDao) loadDidaDestinationsWithMultilingual(ctx context.Context, didaDir string, countries map[string]*domain.Region) map[string][]*domain.Region {
	destinationsByCountry := make(map[string][]*domain.Region)

	// Get all destination files
	files, err := ioutil.ReadDir(didaDir)
	if err != nil {
		logx.WithContext(ctx).Errorf("Failed to read dida directory for destinations: %v", err)
		return destinationsByCountry
	}

	// Group files by country
	enFiles := make(map[string]string) // countryCode -> filename
	zhFiles := make(map[string]string) // countryCode -> filename

	for _, f := range files {
		if f.IsDir() {
			continue
		}
		if strings.HasPrefix(f.Name(), "destinations_") {
			parts := strings.Split(f.Name(), "_")
			if len(parts) >= 3 {
				countryCode := parts[1]
				if strings.HasSuffix(f.Name(), "_en-US.json") {
					enFiles[countryCode] = f.Name()
				} else if strings.HasSuffix(f.Name(), "_zh-CN.json") {
					zhFiles[countryCode] = f.Name()
				}
			}
		}
	}

	// Process each country
	for countryCode := range enFiles {
		countryRegion := countries[countryCode]
		if countryRegion == nil {
			logx.WithContext(ctx).Infof("Country region not found for code %s", countryCode)
			continue
		}

		// Load English destinations
		enDestinations := r.loadDestinationsFromFile(ctx, didaDir, enFiles[countryCode], countryRegion)

		// Load Chinese destinations if available
		var zhDestinations map[string]*domain.Region
		if zhFile, exists := zhFiles[countryCode]; exists {
			zhDestinationsList := r.loadDestinationsFromFile(ctx, didaDir, zhFile, countryRegion)
			zhDestinations = make(map[string]*domain.Region)
			for _, dest := range zhDestinationsList {
				zhDestinations[dest.DidaId] = dest
			}
		}

		// Merge Chinese names into English destinations
		for _, enDest := range enDestinations {
			if zhDestinations != nil {
				if zhDest, exists := zhDestinations[enDest.DidaId]; exists {
					// Set Chinese name from Chinese version
					enDest.Extra.NameZh = zhDest.Name
				}
			}
		}

		destinationsByCountry[countryCode] = enDestinations
	}

	return destinationsByCountry
}

// loadDestinationsFromFile loads destinations from a specific file
func (r *RegionDao) loadDestinationsFromFile(ctx context.Context, didaDir, filename string, countryRegion *domain.Region) []*domain.Region {
	destinations := make([]*domain.Region, 0)

	filePath := filepath.Join(didaDir, filename)
	fileContent := utils.ReadFile(filePath)
	if fileContent == nil {
		logx.WithContext(ctx).Errorf("Failed to read Dida destinations file %s", filename)
		return destinations
	}

	var destinationsFile domain.DidaDestinationsFile
	if err := json.Unmarshal(fileContent, &destinationsFile); err != nil {
		log.Errorc(ctx, "Failed to unmarshal Dida destinations file %s: %v", filename, err)
		return destinations
	}

	for _, dest := range destinationsFile.Destinations {
		region := dest.ToRegion(countryRegion)
		destinations = append(destinations, region)
	}

	return destinations
}

// RegionMatcher provides optimized region matching with indexing and caching
type RegionMatcher struct {
	// Primary index: countryCode -> type -> normalizedName -> []*domain.Region
	ctripIndex map[string]map[domain.RegionType]map[string][]*domain.Region

	// Cache for normalized names to avoid recomputation
	normalizedNameCache map[string]string

	// Bidirectional English-Chinese name lookup for O(1) access
	engToChineseMap map[string]string
	chineseToEngMap map[string]string

	// Compatible type pairs for O(1) lookup
	compatibleTypes map[domain.RegionType][]domain.RegionType

	// Statistics
	totalCtripRegions int
	indexBuildTime    time.Duration
}

// mergeRegionsFromSuppliers merges regions from different suppliers using optimized indexing and country-based partitioning
func (r *RegionDao) mergeRegionsFromSuppliers(ctx context.Context, ctripRegions, didaRegions []*domain.Region) []*domain.Region {
	startTime := time.Now()

	// Flatten Ctrip regions - extract cities from Descendants
	flatCtripRegions := r.flattenCtripRegions(ctx, ctripRegions)

	// Build optimized matcher with indexing
	matcher := r.buildRegionMatcher(ctx, flatCtripRegions, didaRegions)

	// Group regions by country for optimized processing
	ctripByCountry := r.groupRegionsByCountry(flatCtripRegions)
	didaByCountry := r.groupRegionsByCountry(didaRegions)

	// Merge regions country by country
	mergedRegions := make([]*domain.Region, 0)
	totalDidaAdded := 0
	totalDidaMerged := 0
	mergedExamples := make([]string, 0)

	// Process each country independently
	for countryCode := range ctripByCountry {
		ctripCountryRegions := ctripByCountry[countryCode]
		didaCountryRegions := didaByCountry[countryCode] // May be nil if no Dida regions for this country

		// Add all Ctrip regions for this country first
		for _, region := range ctripCountryRegions {
			mergedRegions = append(mergedRegions, region)
		}

		// Merge Dida regions for this country if any exist
		if len(didaCountryRegions) > 0 {
			didaAdded, didaMerged, examples := r.mergeDidaRegionsForCountry(ctx, matcher, countryCode, ctripCountryRegions, didaCountryRegions)
			totalDidaAdded += didaAdded
			totalDidaMerged += didaMerged

			// Collect examples (limit to 10 total)
			for _, example := range examples {
				if len(mergedExamples) < 10 {
					mergedExamples = append(mergedExamples, example)
				}
			}

			// Add unmatched Dida regions
			for _, didaRegion := range didaCountryRegions {
				// Check if this Dida region was merged (has TripId set during merge)
				if didaRegion.SupplierID.TripId == "" {
					mergedRegions = append(mergedRegions, didaRegion)
				}
			}
		}
	}

	// Process Dida regions for countries not in Ctrip data
	for countryCode, didaCountryRegions := range didaByCountry {
		if _, exists := ctripByCountry[countryCode]; !exists {
			// Add all Dida regions for countries not covered by Ctrip
			for _, region := range didaCountryRegions {
				mergedRegions = append(mergedRegions, region)
				totalDidaAdded++
			}
		}
	}

	totalTime := time.Since(startTime)

	logx.WithContext(ctx).Infof("Merge summary: %d Ctrip (flattened from %d), %d Dida added, %d Dida merged, %d total countries, %d total regions, took %v",
		len(flatCtripRegions), len(ctripRegions), totalDidaAdded, totalDidaMerged, len(ctripByCountry)+len(didaByCountry), len(mergedRegions), totalTime)

	logx.WithContext(ctx).Infof("Index build time: %v, Total time: %v", matcher.indexBuildTime, totalTime)
	return mergedRegions
}

// flattenCtripRegions recursively extracts all regions from Ctrip Descendants to create a flat structure
func (r *RegionDao) flattenCtripRegions(ctx context.Context, ctripRegions []*domain.Region) []*domain.Region {
	flatRegions := make([]*domain.Region, 0)

	for _, region := range ctripRegions {
		// Recursively flatten this region and all its descendants
		r.flattenRegionRecursive(region, &flatRegions)
	}

	logx.WithContext(ctx).Infof("Flattened %d Ctrip regions into %d total regions (including all descendants)",
		len(ctripRegions), len(flatRegions))

	return flatRegions
}

// flattenRegionRecursive recursively flattens a region and all its descendants
func (r *RegionDao) flattenRegionRecursive(region *domain.Region, flatRegions *[]*domain.Region) {
	// Add the current region
	*flatRegions = append(*flatRegions, region)

	// Recursively process all descendants
	if region.Descendants != nil {
		for _, descendant := range region.Descendants {
			// Make sure the descendant has proper supplier ID
			if descendant.SupplierID.TripId == "" {
				descendant.SupplierID.TripId = descendant.ID.String()
			}

			// Recursively flatten the descendant and its descendants
			r.flattenRegionRecursive(descendant, flatRegions)
		}
	}
}

// shouldMergeRegions determines if two regions should be merged using intelligent matching
func (r *RegionDao) shouldMergeRegions(ctripRegion, didaRegion *domain.Region) bool {
	// Must be same country
	if ctripRegion.CountryCode != didaRegion.CountryCode {
		return false
	}

	// Must be compatible types
	if !r.areTypesCompatible(ctripRegion.Type, didaRegion.Type) {
		return false
	}

	// Don't merge if Ctrip region already has a DidaId
	if ctripRegion.SupplierID.DidaId != "" {
		return false
	}

	// Normalize names for comparison
	ctripName := r.normalizeRegionName(ctripRegion.Name)
	didaName := r.normalizeRegionName(didaRegion.Name)

	// Check for exact match after normalization
	if ctripName == didaName {
		return true
	}

	// Check if one name contains the other (for cases like "上海" vs "上海 (及其周边地区)")
	if strings.Contains(didaName, ctripName) || strings.Contains(ctripName, didaName) {
		return true
	}

	// Check for common English-Chinese name pairs
	if r.isEnglishChineseNamePair(ctripName, didaName) {
		return true
	}

	return false
}

// areTypesCompatible checks if two region types are compatible for merging
func (r *RegionDao) areTypesCompatible(type1, type2 domain.RegionType) bool {
	// Exact match is always compatible
	if type1 == type2 {
		return true
	}

	// Type:5 (multi_city_vicinity) and Type:6 (city) are compatible
	// Both represent city-level regions
	if (type1 == domain.RegionType_MultiCityVicinity && type2 == domain.RegionType_City) ||
		(type1 == domain.RegionType_City && type2 == domain.RegionType_MultiCityVicinity) {
		return true
	}

	// Type:2 (country) and Type:3 (province_state) might be compatible for some edge cases
	if (type1 == domain.RegionType_Country && type2 == domain.RegionType_ProvinceState) ||
		(type1 == domain.RegionType_ProvinceState && type2 == domain.RegionType_Country) {
		return true
	}

	// Type:6 (city) and Type:3 (province_state) are compatible
	// Counties and prefecture-level cities can match with province-level regions
	if (type1 == domain.RegionType_City && type2 == domain.RegionType_ProvinceState) ||
		(type1 == domain.RegionType_ProvinceState && type2 == domain.RegionType_City) {
		return true
	}

	// Type:5 (multi_city_vicinity) and Type:3 (province_state) are compatible
	// Major cities can match with province-level regions
	if (type1 == domain.RegionType_MultiCityVicinity && type2 == domain.RegionType_ProvinceState) ||
		(type1 == domain.RegionType_ProvinceState && type2 == domain.RegionType_MultiCityVicinity) {
		return true
	}

	return false
}

// normalizeRegionName normalizes region names for comparison
func (r *RegionDao) normalizeRegionName(name string) string {
	// Convert to lowercase
	normalized := strings.ToLower(strings.TrimSpace(name))

	// Remove common suffixes and prefixes
	suffixes := []string{
		" (及周边地区)", " (及其周边地区)", " (and vicinity)", " (and surrounding areas)",
		" province", " city", " municipality", " autonomous region",
		"省", "市", "自治区", "特别行政区",
	}

	for _, suffix := range suffixes {
		normalized = strings.TrimSuffix(normalized, strings.ToLower(suffix))
	}

	// Remove extra spaces
	normalized = strings.TrimSpace(normalized)

	return normalized
}

// isEnglishChineseNamePair checks if two names are English-Chinese pairs
func (r *RegionDao) isEnglishChineseNamePair(name1, name2 string) bool {
	// Common English-Chinese name pairs for major cities and provinces
	commonPairs := map[string]string{
		// Major cities
		"beijing":      "北京",
		"shanghai":     "上海",
		"guangzhou":    "广州",
		"shenzhen":     "深圳",
		"hangzhou":     "杭州",
		"nanjing":      "南京",
		"chengdu":      "成都",
		"wuhan":        "武汉",
		"chongqing":    "重庆",
		"tianjin":      "天津",
		"xian":         "西安",
		"suzhou":       "苏州",
		"dalian":       "大连",
		"qingdao":      "青岛",
		"xiamen":       "厦门",
		"kunming":      "昆明",
		"harbin":       "哈尔滨",
		"shenyang":     "沈阳",
		"changchun":    "长春",
		"taiyuan":      "太原",
		"shijiazhuang": "石家庄",
		"zhengzhou":    "郑州",
		"jinan":        "济南",
		"hefei":        "合肥",
		"nanchang":     "南昌",
		"changsha":     "长沙",
		"fuzhou":       "福州",
		"nanning":      "南宁",
		"haikou":       "海口",
		"guiyang":      "贵阳",
		"lanzhou":      "兰州",
		"xining":       "西宁",
		"yinchuan":     "银川",
		"urumqi":       "乌鲁木齐",
		"lhasa":        "拉萨",

		// Provinces and regions
		"guangdong":      "广东",
		"jiangsu":        "江苏",
		"shandong":       "山东",
		"zhejiang":       "浙江",
		"henan":          "河南",
		"sichuan":        "四川",
		"hubei":          "湖北",
		"hunan":          "湖南",
		"anhui":          "安徽",
		"hebei":          "河北",
		"jiangxi":        "江西",
		"shanxi":         "山西",
		"liaoning":       "辽宁",
		"fujian":         "福建",
		"shaanxi":        "陕西",
		"heilongjiang":   "黑龙江",
		"jilin":          "吉林",
		"guangxi":        "广西",
		"yunnan":         "云南",
		"guizhou":        "贵州",
		"gansu":          "甘肃",
		"hainan":         "海南",
		"qinghai":        "青海",
		"ningxia":        "宁夏",
		"xinjiang":       "新疆",
		"tibet":          "西藏",
		"inner mongolia": "内蒙古",
		"hong kong":      "香港",
		"macau":          "澳门",
		"taiwan":         "台湾",
	}

	// Check both directions
	for eng, chn := range commonPairs {
		if (strings.Contains(name1, eng) && strings.Contains(name2, chn)) ||
			(strings.Contains(name2, eng) && strings.Contains(name1, chn)) {
			return true
		}
	}

	return false
}

// buildRegionMatcher creates an optimized matcher with indexing and caching
func (r *RegionDao) buildRegionMatcher(ctx context.Context, ctripRegions, didaRegions []*domain.Region) *RegionMatcher {
	startTime := time.Now()

	matcher := &RegionMatcher{
		ctripIndex:          make(map[string]map[domain.RegionType]map[string][]*domain.Region),
		normalizedNameCache: make(map[string]string),
		engToChineseMap:     make(map[string]string),
		chineseToEngMap:     make(map[string]string),
		compatibleTypes:     make(map[domain.RegionType][]domain.RegionType),
		totalCtripRegions:   len(ctripRegions),
	}

	// Build compatible types lookup table
	matcher.buildCompatibleTypesTable()

	// Build English-Chinese name pair maps from real data
	matcher.buildNamePairMapsFromData(ctripRegions, didaRegions)

	// Add hard-coded country name mappings for known variations
	matcher.addCountryNameMappings()

	// Build primary index: countryCode -> type -> normalizedName -> []*domain.Region
	for _, region := range ctripRegions {
		countryCode := region.CountryCode
		regionType := region.Type
		normalizedName := matcher.getNormalizedName(region.Name)

		// Initialize nested maps if needed
		if matcher.ctripIndex[countryCode] == nil {
			matcher.ctripIndex[countryCode] = make(map[domain.RegionType]map[string][]*domain.Region)
		}
		if matcher.ctripIndex[countryCode][regionType] == nil {
			matcher.ctripIndex[countryCode][regionType] = make(map[string][]*domain.Region)
		}

		// Add region to index
		matcher.ctripIndex[countryCode][regionType][normalizedName] = append(
			matcher.ctripIndex[countryCode][regionType][normalizedName], region)
	}

	matcher.indexBuildTime = time.Since(startTime)

	logx.WithContext(ctx).Infof("Built region matcher index: %d regions, %d countries, %d cached names, took %v",
		len(ctripRegions), len(matcher.ctripIndex), len(matcher.normalizedNameCache), matcher.indexBuildTime)

	return matcher
}

// buildCompatibleTypesTable pre-computes compatible type pairs for O(1) lookup
func (matcher *RegionMatcher) buildCompatibleTypesTable() {
	// Each type is compatible with itself
	allTypes := []domain.RegionType{
		domain.RegionType_Country,
		domain.RegionType_ProvinceState,
		domain.RegionType_City,
		domain.RegionType_MultiCityVicinity,
	}

	for _, t := range allTypes {
		matcher.compatibleTypes[t] = []domain.RegionType{t}
	}

	// Add cross-compatible types based on areTypesCompatible logic
	matcher.compatibleTypes[domain.RegionType_MultiCityVicinity] = append(
		matcher.compatibleTypes[domain.RegionType_MultiCityVicinity],
		domain.RegionType_City, domain.RegionType_ProvinceState)

	matcher.compatibleTypes[domain.RegionType_City] = append(
		matcher.compatibleTypes[domain.RegionType_City],
		domain.RegionType_MultiCityVicinity, domain.RegionType_ProvinceState)

	matcher.compatibleTypes[domain.RegionType_Country] = append(
		matcher.compatibleTypes[domain.RegionType_Country],
		domain.RegionType_ProvinceState)

	matcher.compatibleTypes[domain.RegionType_ProvinceState] = append(
		matcher.compatibleTypes[domain.RegionType_ProvinceState],
		domain.RegionType_Country, domain.RegionType_City, domain.RegionType_MultiCityVicinity)
}

// getCountryNameMappings returns hard-coded country name variations for known mismatches
func getCountryNameMappings() map[string]string {
	return map[string]string{
		// Political/Administrative differences
		"czech":                             "czech republic",
		"syrian arab republic":              "syria",
		"congo":                             "republic of the congo",
		"usa":                               "united states",
		"micronesia, federated states of":   "micronesia",
		"vatican city state":                "vatican city",
		"heard island and mcdonald islands": "heard island",

		// Special territories and regions
		"hong kong":            "hong kong sar",
		"taiwan":               "taiwan, province of china",
		"western sahara":       "sahara",
		"netherlands antilles": "netherlands", // Dissolved, map to Netherlands
		"french west indies":   "france",      // Informal region, map to France

		// Common variations
		"south korea": "korea, republic of",
		"north korea": "korea, democratic people's republic of",
		"russia":      "russian federation",
		"iran":        "iran, islamic republic of",
		"venezuela":   "venezuela, bolivarian republic of",
		"bolivia":     "bolivia, plurinational state of",
		"tanzania":    "tanzania, united republic of",
		"moldova":     "moldova, republic of",
		"macedonia":   "north macedonia",
		"cape verde":  "cabo verde",

		// Island nations and territories
		"saint martin":           "saint martin (french part)",
		"british virgin islands": "virgin islands, british",
		"us virgin islands":      "virgin islands, u.s.",
		"falkland islands":       "falkland islands (malvinas)",
		"cocos islands":          "cocos (keeling) islands",
		"christmas island":       "christmas island",
		"heard island":           "heard island and mcdonald islands",

		// Reverse mappings for common cases
		"united states":  "usa",
		"united kingdom": "uk",
		"czech republic": "czech",
		"syria":          "syrian arab republic",
	}
}

// buildNamePairMapsFromData creates bidirectional English-Chinese name lookup maps from real data
func (matcher *RegionMatcher) buildNamePairMapsFromData(ctripRegions, didaRegions []*domain.Region) {
	// Extract name pairs from Ctrip regions (Name + NameZh)
	for _, region := range ctripRegions {
		if region.Name != "" && region.Extra.NameZh != "" {
			engName := matcher.getNormalizedName(region.Name)
			chName := strings.TrimSpace(region.Extra.NameZh)

			if engName != "" && chName != "" {
				matcher.engToChineseMap[engName] = chName
				matcher.chineseToEngMap[chName] = engName
			}
		}
	}

	// Extract name pairs from Dida regions (Name + NameZh)
	for _, region := range didaRegions {
		if region.Name != "" && region.Extra.NameZh != "" {
			engName := matcher.getNormalizedName(region.Name)
			chName := strings.TrimSpace(region.Extra.NameZh)

			if engName != "" && chName != "" {
				// Only add if not already exists (Ctrip data takes priority)
				if _, exists := matcher.engToChineseMap[engName]; !exists {
					matcher.engToChineseMap[engName] = chName
				}
				if _, exists := matcher.chineseToEngMap[chName]; !exists {
					matcher.chineseToEngMap[chName] = engName
				}
			}
		}
	}

	logx.Infof("Built name pair maps from real data: %d English->Chinese, %d Chinese->English pairs",
		len(matcher.engToChineseMap), len(matcher.chineseToEngMap))
}

// addCountryNameMappings adds hard-coded country name variations to the matcher
func (matcher *RegionMatcher) addCountryNameMappings() {
	countryMappings := getCountryNameMappings()
	addedCount := 0

	for from, to := range countryMappings {
		fromNorm := matcher.getNormalizedName(from)
		toNorm := matcher.getNormalizedName(to)

		if fromNorm != "" && toNorm != "" {
			// Add bidirectional mapping
			if _, exists := matcher.engToChineseMap[fromNorm]; !exists {
				matcher.engToChineseMap[fromNorm] = toNorm
				addedCount++
			}
			if _, exists := matcher.engToChineseMap[toNorm]; !exists {
				matcher.engToChineseMap[toNorm] = fromNorm
				addedCount++
			}
		}
	}

	logx.Infof("Added %d hard-coded country name mappings", addedCount)
}

// getNormalizedName gets normalized name with caching
func (matcher *RegionMatcher) getNormalizedName(name string) string {
	if normalized, exists := matcher.normalizedNameCache[name]; exists {
		return normalized
	}

	// Compute normalized name (same logic as normalizeRegionName)
	normalized := strings.ToLower(strings.TrimSpace(name))

	// Remove common suffixes and prefixes
	suffixes := []string{
		" (及周边地区)", " (及其周边地区)", " (and vicinity)", " (and surrounding areas)",
		" province", " city", " municipality", " autonomous region",
		"省", "市", "自治区", "特别行政区",
	}

	for _, suffix := range suffixes {
		normalized = strings.TrimSuffix(normalized, strings.ToLower(suffix))
	}

	normalized = strings.TrimSpace(normalized)

	// Cache the result
	matcher.normalizedNameCache[name] = normalized
	return normalized
}

// groupRegionsByCountry groups regions by country code for optimized processing
func (r *RegionDao) groupRegionsByCountry(regions []*domain.Region) map[string][]*domain.Region {
	grouped := make(map[string][]*domain.Region)

	for _, region := range regions {
		countryCode := region.CountryCode
		grouped[countryCode] = append(grouped[countryCode], region)
	}

	return grouped
}

// mergeDidaRegionsForCountry merges Dida regions with Ctrip regions for a specific country using optimized indexing
func (r *RegionDao) mergeDidaRegionsForCountry(ctx context.Context, matcher *RegionMatcher, countryCode string, ctripRegions, didaRegions []*domain.Region) (int, int, []string) {
	didaAdded := 0
	didaMerged := 0
	examples := make([]string, 0)

	// Get country-specific index
	countryIndex := matcher.ctripIndex[countryCode]
	if countryIndex == nil {
		// No Ctrip regions for this country, all Dida regions will be added
		return len(didaRegions), 0, examples
	}

	// Performance optimization: limit processing for countries with too many regions
	maxRegionsToProcess := 5000
	if len(didaRegions) > maxRegionsToProcess {
		//logx.WithContext(ctx).Infof("Country [%s] has %d Dida regions, limiting to %d for performance",
		//	countryCode, len(didaRegions), maxRegionsToProcess)
		didaRegions = didaRegions[:maxRegionsToProcess]
	}

	for _, didaRegion := range didaRegions {
		matchFound := false

		// Get compatible types for this Dida region
		compatibleTypes := matcher.compatibleTypes[didaRegion.Type]
		if compatibleTypes == nil {
			compatibleTypes = []domain.RegionType{didaRegion.Type} // Fallback to exact type match
		}

		// Try to find match using optimized index lookup
		didaName := matcher.getNormalizedName(didaRegion.Name)

		for _, compatibleType := range compatibleTypes {
			typeIndex := countryIndex[compatibleType]
			if typeIndex == nil {
				continue
			}

			// Direct lookup by normalized name
			if candidates := typeIndex[didaName]; len(candidates) > 0 {
				// Found exact normalized name match
				ctripRegion := r.findBestMatch(candidates, didaRegion, matcher)
				if ctripRegion != nil {
					r.mergeRegionData(ctripRegion, didaRegion)
					didaMerged++
					matchFound = true

					// Collect example
					if len(examples) < 5 { // Limit examples per country
						examples = append(examples, fmt.Sprintf(
							"[%s] Ctrip:'%s' + Dida:'%s' (TripId:%s + DidaId:%s)",
							countryCode, ctripRegion.Name, didaRegion.Name,
							ctripRegion.SupplierID.TripId, ctripRegion.SupplierID.DidaId))
					}

					// logx.WithContext(ctx).Debugf("Optimized merge [%s]: Ctrip:'%s' + Dida:'%s' -> TripId:%s, DidaId:%s",
					// 	countryCode, ctripRegion.Name, didaRegion.Name, ctripRegion.SupplierID.TripId, ctripRegion.SupplierID.DidaId)
					break
				}
			}

			// If no exact match, try fuzzy matching within the same type
			if !matchFound {
				matchFound = r.tryFuzzyMatch(ctx, typeIndex, didaRegion, matcher, &examples, countryCode)
				if matchFound {
					didaMerged++
					break
				}
			}
		}

		if !matchFound {
			didaAdded++
		}
	}

	return didaAdded, didaMerged, examples
}

// findBestMatch finds the best matching Ctrip region from candidates
func (r *RegionDao) findBestMatch(candidates []*domain.Region, didaRegion *domain.Region, matcher *RegionMatcher) *domain.Region {
	for _, candidate := range candidates {
		// Don't merge if Ctrip region already has a DidaId
		if candidate.SupplierID.DidaId == "" {
			return candidate
		}
	}
	return nil
}

// mergeRegionData merges Dida data into Ctrip region
func (r *RegionDao) mergeRegionData(ctripRegion, didaRegion *domain.Region) {
	// Merge Dida data into existing Ctrip region
	ctripRegion.SupplierID.DidaId = didaRegion.SupplierID.DidaId

	// Merge Chinese name if available and not already set
	if didaRegion.Extra.NameZh != "" && ctripRegion.Extra.NameZh == "" {
		ctripRegion.Extra.NameZh = didaRegion.Extra.NameZh
	}

	// Mark the Dida region as merged by setting its TripId
	// This prevents it from being added as a separate region later
	didaRegion.SupplierID.TripId = ctripRegion.SupplierID.TripId
}

// tryFuzzyMatch attempts fuzzy matching within a type index with performance optimizations
func (r *RegionDao) tryFuzzyMatch(ctx context.Context, typeIndex map[string][]*domain.Region, didaRegion *domain.Region, matcher *RegionMatcher, examples *[]string, countryCode string) bool {
	didaName := matcher.getNormalizedName(didaRegion.Name)
	didaNameLen := len(didaName)

	// Performance optimization: limit fuzzy matching to reasonable candidates
	maxCandidates := 100 // Limit fuzzy matching to first 100 candidates
	candidateCount := 0

	// Try substring matching and English-Chinese name pairs
	for indexedName, candidates := range typeIndex {
		candidateCount++
		if candidateCount > maxCandidates {
			break // Stop after checking reasonable number of candidates
		}

		// Quick length-based filtering to avoid expensive string operations
		indexedNameLen := len(indexedName)
		if abs(didaNameLen-indexedNameLen) > max(didaNameLen, indexedNameLen)/2 {
			continue // Skip if length difference is too large
		}

		if r.namesMatch(didaName, indexedName, matcher) {
			ctripRegion := r.findBestMatch(candidates, didaRegion, matcher)
			if ctripRegion != nil {
				r.mergeRegionData(ctripRegion, didaRegion)

				// Collect example
				if len(*examples) < 5 {
					*examples = append(*examples, fmt.Sprintf(
						"[%s] Fuzzy: Ctrip:'%s' + Dida:'%s' (TripId:%s + DidaId:%s)",
						countryCode, ctripRegion.Name, didaRegion.Name,
						ctripRegion.SupplierID.TripId, ctripRegion.SupplierID.DidaId))
				}

				logx.WithContext(ctx).Debugf("Fuzzy match [%s]: Ctrip:'%s' + Dida:'%s' -> TripId:%s, DidaId:%s",
					countryCode, ctripRegion.Name, didaRegion.Name, ctripRegion.SupplierID.TripId, ctripRegion.SupplierID.DidaId)
				return true
			}
		}
	}

	return false
}

// Helper functions for performance optimization
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// namesMatch checks if two normalized names match using various strategies with performance optimizations
func (r *RegionDao) namesMatch(name1, name2 string, matcher *RegionMatcher) bool {
	// Quick exact match check first
	if name1 == name2 {
		return true
	}

	// Performance optimization: skip very short or very different length names
	len1, len2 := len(name1), len(name2)
	if len1 < 2 || len2 < 2 || abs(len1-len2) > max(len1, len2)*2/3 {
		return false
	}

	// Check for English-Chinese name pairs using optimized lookup (faster than contains)
	if matcher.engToChineseMap[name1] == name2 || matcher.chineseToEngMap[name1] == name2 ||
		matcher.engToChineseMap[name2] == name1 || matcher.chineseToEngMap[name2] == name1 {
		return true
	}

	// Check if one name contains the other (for cases like "上海" vs "上海 (及其周边地区)")
	// Only do expensive contains check if other conditions suggest a possible match
	if len1 <= len2*2 && len2 <= len1*2 { // Reasonable length ratio
		if strings.Contains(name1, name2) || strings.Contains(name2, name1) {
			return true
		}
	}

	return false
}

// getRegionKey generates a unique key for region matching (kept for backward compatibility)
func (r *RegionDao) getRegionKey(region *domain.Region) string {
	// Use country code + normalized name for matching
	normalizedName := strings.ToLower(strings.TrimSpace(region.Name))
	return fmt.Sprintf("%s:%s:%d", region.CountryCode, normalizedName, region.Type)
}

//mon:gen
func (r *RegionDao) Save(ctx context.Context, in ...*domain.Region) (err error) {
	toInsert := make([]*Region, 0)
	updatedCnt := 0
	for _, v := range in {
		mdl := convertDomain2Model(v)
		if _, err = r.rm.FindOne(ctx, mdl.Id); errors.Is(err, ErrNotFound) {
			toInsert = append(toInsert, mdl)
			continue
		}
		err = r.rm.Update(ctx, mdl)
		if err != nil {
			return err
		}
		updatedCnt++
	}
	if err = r.rm.BatchInsert(ctx, toInsert...); err != nil {
		logx.Errorf("BatchInsert err(%+v)", err)
	}
	logx.WithContext(ctx).Infof("Save %d, Inserted %d, Updated %d", len(in), len(toInsert), updatedCnt)
	return nil
}

//mon:gen
func (r *RegionDao) LoadAllRegions(ctx context.Context) ([]*domain.Region, error) {
	var allRegions []*domain.Region
	var lastID int64 = 0
	const batchSize = 1000

	for {
		rows, err := r.db.QueryContext(ctx, `
            SELECT id, type, name, name_full, expedia_id, trip_id, dida_id,
                   country_code, country_subdivision_code, coordinates,
                   ancestors, extra
            FROM region
            WHERE is_deleted = 0 AND id > ?
            ORDER BY id
            LIMIT ?
        `, lastID, batchSize)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		var regions []*domain.Region
		for rows.Next() {
			var region domain.Region
			var ancestorsJSON, coordinatesJSON, extraJSON []byte

			if err := rows.Scan(
				&region.ID, &region.Type, &region.Name, &region.NameFull,
				&region.ExpediaId, &region.TripId, &region.DidaId,
				&region.CountryCode, &region.CountrySubdivisionCode,
				&coordinatesJSON, &ancestorsJSON, &extraJSON,
			); err != nil {
				return nil, err
			}

			// 解析 JSON 字段
			if err := json.Unmarshal(coordinatesJSON, &region.Coordinates); err != nil {
				logx.Errorf("coordinates: %v id(%d) when parse: %s", err, region.ID, coordinatesJSON)
			}
			if err := json.Unmarshal(ancestorsJSON, &region.Ancestors); err != nil {
				logx.Errorf("ancestors: %v id(%d) when parse: %s", err, region.ID, coordinatesJSON)
			}
			if err := json.Unmarshal(extraJSON, &region.Extra); err != nil {
				logx.Errorf("extra: %v id(%d) when parse: %s", err, region.ID, coordinatesJSON)
			}

			// 在数据库读取时优化NameFull
			region.OptimizeNameFull()
			regions = append(regions, &region)
		}

		if len(regions) == 0 {
			break
		}

		allRegions = append(allRegions, regions...)
		lastID = regions[len(regions)-1].ID.Int64()
		time.Sleep(100 * time.Millisecond)
	}

	return allRegions, nil
}

// SyncStats represents the statistics of a sync operation
type SyncStats struct {
	TotalFileRegions int `json:"totalFileRegions"`
	TotalDbRegions   int `json:"totalDbRegions"`
	NewRegions       int `json:"newRegions"`
	UpdatedRegions   int `json:"updatedRegions"`
	DeletedRegions   int `json:"deletedRegions"`
	ErrorCount       int `json:"errorCount"`
}

//mon:gen
func (r *RegionDao) SyncLocalFilesToDB(ctx context.Context) (*SyncStats, error) {
	stats := &SyncStats{}

	// Load regions from local files
	fileRegions, err := r.LoadAllRegionsFromFile(ctx)
	if err != nil {
		return stats, fmt.Errorf("failed to load regions from files: %w", err)
	}
	stats.TotalFileRegions = len(fileRegions)
	logx.WithContext(ctx).Infof("Loaded %d regions from local files", len(fileRegions))

	// Load existing regions from database
	dbRegions, err := r.LoadAllRegions(ctx)
	if err != nil {
		return stats, fmt.Errorf("failed to load regions from database: %w", err)
	}
	stats.TotalDbRegions = len(dbRegions)
	logx.WithContext(ctx).Infof("Loaded %d regions from database", len(dbRegions))

	// Create maps for efficient lookup
	fileRegionMap := make(map[types.ID]*domain.Region)
	for _, region := range fileRegions {
		fileRegionMap[region.ID] = region
	}

	dbRegionMap := make(map[types.ID]*domain.Region)
	for _, region := range dbRegions {
		dbRegionMap[region.ID] = region
	}

	// Find new and updated regions
	var toInsert []*domain.Region
	var toUpdate []*domain.Region

	for id, fileRegion := range fileRegionMap {
		if dbRegion, exists := dbRegionMap[id]; exists {
			// Check if region needs update (compare key fields)
			if r.needsUpdate(fileRegion, dbRegion) {
				toUpdate = append(toUpdate, fileRegion)
			}
		} else {
			// New region
			toInsert = append(toInsert, fileRegion)
		}
	}

	// Find deleted regions (exist in DB but not in files)
	var toDelete []types.ID
	for id := range dbRegionMap {
		if _, exists := fileRegionMap[id]; !exists {
			toDelete = append(toDelete, id)
		}
	}

	// Perform batch operations
	if len(toInsert) > 0 {
		if err := r.batchInsertRegions(ctx, toInsert); err != nil {
			logx.WithContext(ctx).Errorf("Failed to insert %d regions: %v", len(toInsert), err)
			stats.ErrorCount++
		} else {
			stats.NewRegions = len(toInsert)
			logx.WithContext(ctx).Infof("Inserted %d new regions", len(toInsert))
		}
	}

	if len(toUpdate) > 0 {
		if err := r.batchUpdateRegions(ctx, toUpdate); err != nil {
			logx.WithContext(ctx).Errorf("Failed to update %d regions: %v", len(toUpdate), err)
			stats.ErrorCount++
		} else {
			stats.UpdatedRegions = len(toUpdate)
			logx.WithContext(ctx).Infof("Updated %d regions", len(toUpdate))
		}
	}

	if len(toDelete) > 0 {
		if err := r.batchSoftDeleteRegions(ctx, toDelete); err != nil {
			logx.WithContext(ctx).Errorf("Failed to delete %d regions: %v", len(toDelete), err)
			stats.ErrorCount++
		} else {
			stats.DeletedRegions = len(toDelete)
			logx.WithContext(ctx).Infof("Soft deleted %d regions", len(toDelete))
		}
	}

	logx.WithContext(ctx).Infof("Sync completed: %+v", stats)
	return stats, nil
}

// needsUpdate compares two regions to determine if an update is needed
func (r *RegionDao) needsUpdate(fileRegion, dbRegion *domain.Region) bool {
	// Compare key fields that might change
	return fileRegion.Name != dbRegion.Name ||
		fileRegion.NameFull != dbRegion.NameFull ||
		fileRegion.ExpediaId != dbRegion.ExpediaId ||
		fileRegion.TripId != dbRegion.TripId ||
		fileRegion.DidaId != dbRegion.DidaId ||
		fileRegion.CountryCode != dbRegion.CountryCode ||
		fileRegion.CountrySubdivisionCode != dbRegion.CountrySubdivisionCode ||
		utils.ToJSON(fileRegion.Coordinates) != utils.ToJSON(dbRegion.Coordinates) ||
		utils.ToJSON(fileRegion.Extra) != utils.ToJSON(dbRegion.Extra)
}

// batchInsertRegions performs batch insert of new regions
func (r *RegionDao) batchInsertRegions(ctx context.Context, regions []*domain.Region) error {
	const batchSize = 100
	for i := 0; i < len(regions); i += batchSize {
		end := i + batchSize
		if end > len(regions) {
			end = len(regions)
		}
		batch := regions[i:end]

		models := make([]*Region, len(batch))
		for j, region := range batch {
			models[j] = convertDomain2Model(region)
		}

		if err := r.rm.BatchInsert(ctx, models...); err != nil {
			return fmt.Errorf("batch insert failed for batch %d-%d: %w", i, end, err)
		}
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

// batchUpdateRegions performs batch update of existing regions
func (r *RegionDao) batchUpdateRegions(ctx context.Context, regions []*domain.Region) error {
	for _, region := range regions {
		model := convertDomain2Model(region)
		if err := r.rm.Update(ctx, model); err != nil {
			return fmt.Errorf("update failed for region %d: %w", region.ID, err)
		}
	}
	return nil
}

// batchSoftDeleteRegions performs soft delete of regions
func (r *RegionDao) batchSoftDeleteRegions(ctx context.Context, regionIDs []types.ID) error {
	for _, id := range regionIDs {
		query := "UPDATE region SET is_deleted = 1 WHERE id = ?"
		if _, err := r.conn.ExecCtx(ctx, query, id.Int64()); err != nil {
			return fmt.Errorf("soft delete failed for region %d: %w", id, err)
		}
	}
	return nil
}
