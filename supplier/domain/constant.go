package domain

import (
	"github.com/hashicorp/go-set/v3"
	"github.com/spf13/cast"
)

// Package order 统一订单相关枚举和常量
type RebookStatus int

const (
	RebookProcessing RebookStatus = iota + 1
	RebookSuccess
	RebookFail
	RebookCancel
	RebookSubmitted
)

type MetroObjectTypeEnum int

const (
	City MetroObjectTypeEnum = iota + 1
	MetroLine
	MetroStation
)

type OrderStatus int

const (
	OrderStatus_Unknown OrderStatus = iota
	OrderStatus_Submitted
	OrderStatus_Confirming
	OrderStatus_Confirmed
	OrderStatus_Deal
	OrderStatus_Cancelled
	OrderStatus_UnSubmitted
	_
	_
	OrderStatus_Submitting
	OrderStatus_Issuing
)

var (
	CancellableOrderStatusSet = set.From[OrderStatus]([]OrderStatus{
		OrderStatus_Submitted, OrderStatus_Confirming, OrderStatus_Confirmed,
	})
)

func (s OrderStatus) String() string {
	switch s {
	case OrderStatus_Submitted:
		return "OrderStatus_Submitted"
	case OrderStatus_Confirming:
		return "OrderStatus_Confirming"
	case OrderStatus_Confirmed:
		return "OrderStatus_Confirmed"
	case OrderStatus_Deal:
		return "OrderStatus_Deal"
	case OrderStatus_Cancelled:
		return "OrderStatus_Cancelled"
	case OrderStatus_UnSubmitted:
		return "OrderStatus_UnSubmitted"
	case OrderStatus_Submitting:
		return "OrderStatus_Submitting"
	case OrderStatus_Issuing:
		return "OrderStatus_Issuing"
	default:
		return "Unknown"
	}
}

type DomesticZoneOrderEnum int

// 0: City and tenant district names in alphabetical order
// 1: City, Dynamic Result in reverse order, tenant district names in alphabetical order.
// 2: City, Position, Dynamic Result in reverse order
// 3: By the commercial area popularity ratio (the Hits Ratios of commercial areas, which is the order volume of the commercial area
//
//	divided by the total order volume of the city where the commercial area is located), in descending order.
//
// 4: By sales ratio (one month for domestic, three months for overseas - you don't need to worry), in descending order.
const (
	Normal DomesticZoneOrderEnum = iota + 0
	Normal1
	Normal2
	HeatRatio
	SalesRatio
)

type Domain int

const (
	Hotel Domain = iota + 1
	Flight
	Train
)

func (t Domain) String() string {
	switch t {
	case Hotel:
		return "Hotel"
	case Flight:
		return "Flight"
	case Train:
		return "Train"
	default:
		return "Unknown"
	}
}

type EventType int

const (
	SubmittedFailed EventType = iota + 1
	SubmitSuccess
	ConfirmFailed
	ConfirmSuccess
	BookFailed
	BookSuccess
	CancelFailed
	CancelSuccess
	OccupiedSuccess
	OccupiedFailed
	ReBookSubmitted
	ReBookSuccess
	ReBookFailed
	RebookCancelled
)

func (s EventType) String() string {
	switch s {
	case SubmittedFailed:
		return "SubmittedFailed"
	case SubmitSuccess:
		return "SubmitSuccess"
	case ConfirmFailed:
		return "ConfirmFailed"
	case ConfirmSuccess:
		return "ConfirmSuccess"
	case BookFailed:
		return "BookFailed"
	case BookSuccess:
		return "BookSuccess"
	case CancelFailed:
		return "CancelFailed"
	case CancelSuccess:
		return "CancelSuccess"
	case OccupiedSuccess:
		return "OccupiedSuccess"
	case OccupiedFailed:
		return "OccupiedFailed"
	default:
		return "Unknown"
	}
}

func GetHotelEventTypeFromStatus(s OrderStatus) EventType {
	switch s {
	case OrderStatus_UnSubmitted, OrderStatus_Submitted, OrderStatus_Confirming:
		return SubmitSuccess
	case OrderStatus_Confirmed:
		return ConfirmSuccess
	case OrderStatus_Deal:
		return BookSuccess
	case OrderStatus_Cancelled:
		return CancelSuccess
	default:
		return 0
	}
}

type ConfirmType int

const (
	None ConfirmType = iota + 1
	Email
	Message
	Phone
	Fax
)

func (c ConfirmType) String() string {
	switch c {
	case None:
		return "None"
	case Email:
		return "Key"
	case Message:
		return "MobileMSG"
	default:
		return "None"
	}
}

type CertificateType int

const (
	IdentityCardSecond  = iota + 1 // 身份证
	HKMacaoPass                    // 回乡证
	TAIWANPass                     // 台胞证
	HKTRP                          // 港澳台居民居住证
	ForeignRP                      // 外国人永久居留证
	Passport                       // 护照
	HKMacauTravelPermit            // 港澳通行证
	TaiwanTravelPermit             // 台湾通行证
	NationalLocalID                // 国际本地护照 ｜ international local certificate
)

func (c CertificateType) String() string {
	switch c {
	case IdentityCardSecond:
		return "IDENTITY_CARD_SECOND"
	case HKMacaoPass:
		return "HK_MACAO_PASS"
	case TAIWANPass:
		return "TAIWAN_PASS"
	case HKTRP:
		return "HKTRP"
	case ForeignRP:
		return "FOREIGN_RP"
	case Passport:
		return "PASSPORT"
	case HKMacauTravelPermit:
		return "CN_HK_MACAO_PASS"
	case TaiwanTravelPermit:
		return "CN_TAIWAN_PASS"
	case NationalLocalID:
		return "NATIONAL_LOCAL_ID"
	default:
		return "Unknown"
	}
}

type Supplier int

func (s Supplier) Int64() int64 {
	return int64(s)
}
func (s Supplier) String() string {
	//todo: 初始化读 db，加载到全局变量中，这里读全局变量
	return cast.ToString(int64(s))
}

// GetEnglishName returns the English name of the supplier
func (s Supplier) GetEnglishName() string {
	switch s {
	case Supplier_TBO:
		return "TBO"
	case Supplier_DOTW:
		return "DOTW"
	case Supplier_Hotelbeds:
		return "HotelBeds"
	case Supplier_Expedia:
		return "Expedia"
	case Supplier_Trip:
		return "Ctrip"
	case Supplier_Dida:
		return "Dida"
	case Supplier_Almosafer:
		return "Almosafer"
	case Supplier_Agoda:
		return "Agoda"
	case Supplier_YalagoSu:
		return "YalagoSu"
	case Supplier_Emerging:
		return "Emerging"
	case Supplier_Miki:
		return "Miki"
	case Supplier_SmyRoom:
		return "SmyRoom"
	case Supplier_WithinEarth:
		return "WithinEarth"
	case Supplier_Gekko:
		return "Gekko"
	case Supplier_Snyxis:
		return "Snyxis"
	case Supplier_Hyperguest:
		return "Hyperguest"
	case Supplier_TravelGate:
		return "TravelGate"
	case Supplier_Juniper:
		return "Juniper"
	case Supplier_Giata:
		return "GIATA"
	case Supplier_Netstorming:
		return "Netstorming"
	case Supplier_Derbysoft:
		return "DerbySoft"
	case Supplier_Tourmind:
		return "Tourmind"
	case Supplier_Olivier:
		return "Olivier"
	case Supplier_Simulator:
		return "Simulator"
	default:
		return "Unknown"
	}
}

// GetChineseName returns the Chinese name of the supplier
func (s Supplier) GetChineseName() string {
	switch s {
	case Supplier_TBO:
		return "TBO"
	case Supplier_DOTW:
		return "DOTW"
	case Supplier_Hotelbeds:
		return "酒店床位"
	case Supplier_Expedia:
		return "亿客行"
	case Supplier_Trip:
		return "携程"
	case Supplier_Dida:
		return "滴答"
	case Supplier_Almosafer:
		return "阿尔穆萨菲尔"
	case Supplier_Agoda:
		return "安可达"
	case Supplier_YalagoSu:
		return "雅拉戈"
	case Supplier_Emerging:
		return "新兴"
	case Supplier_Miki:
		return "米奇"
	case Supplier_SmyRoom:
		return "智慧客房"
	case Supplier_WithinEarth:
		return "地球内"
	case Supplier_Gekko:
		return "壁虎"
	case Supplier_Snyxis:
		return "斯尼克西斯"
	case Supplier_Hyperguest:
		return "超级客人"
	case Supplier_TravelGate:
		return "旅行门"
	case Supplier_Juniper:
		return "杜松"
	case Supplier_Giata:
		return "吉亚塔"
	case Supplier_Netstorming:
		return "网络风暴"
	case Supplier_Derbysoft:
		return "德比软件"
	case Supplier_Tourmind:
		return "旅游思维"
	case Supplier_Olivier:
		return "奥利维尔"
	case Supplier_Simulator:
		return "模拟器"
	default:
		return "未知"
	}
}

// GetAllSuppliers returns all available suppliers with their details
func GetAllSuppliers() []SupplierInfo {
	return []SupplierInfo{
		{ID: int(Supplier_Dida), Name: Supplier_Dida.GetEnglishName(), ChineseName: Supplier_Dida.GetChineseName(), IsActive: true},
		{ID: int(Supplier_Trip), Name: Supplier_Trip.GetEnglishName(), ChineseName: Supplier_Trip.GetChineseName(), IsActive: true},
		{ID: int(Supplier_Derbysoft), Name: Supplier_Derbysoft.GetEnglishName(), ChineseName: Supplier_Derbysoft.GetChineseName(), IsActive: true},
		{ID: int(Supplier_Hotelbeds), Name: Supplier_Hotelbeds.GetEnglishName(), ChineseName: Supplier_Hotelbeds.GetChineseName(), IsActive: true},
		{ID: int(Supplier_TBO), Name: Supplier_TBO.GetEnglishName(), ChineseName: Supplier_TBO.GetChineseName(), IsActive: false},
		{ID: int(Supplier_DOTW), Name: Supplier_DOTW.GetEnglishName(), ChineseName: Supplier_DOTW.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Expedia), Name: Supplier_Expedia.GetEnglishName(), ChineseName: Supplier_Expedia.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Almosafer), Name: Supplier_Almosafer.GetEnglishName(), ChineseName: Supplier_Almosafer.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Agoda), Name: Supplier_Agoda.GetEnglishName(), ChineseName: Supplier_Agoda.GetChineseName(), IsActive: false},
		{ID: int(Supplier_YalagoSu), Name: Supplier_YalagoSu.GetEnglishName(), ChineseName: Supplier_YalagoSu.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Emerging), Name: Supplier_Emerging.GetEnglishName(), ChineseName: Supplier_Emerging.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Miki), Name: Supplier_Miki.GetEnglishName(), ChineseName: Supplier_Miki.GetChineseName(), IsActive: false},
		{ID: int(Supplier_SmyRoom), Name: Supplier_SmyRoom.GetEnglishName(), ChineseName: Supplier_SmyRoom.GetChineseName(), IsActive: false},
		{ID: int(Supplier_WithinEarth), Name: Supplier_WithinEarth.GetEnglishName(), ChineseName: Supplier_WithinEarth.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Gekko), Name: Supplier_Gekko.GetEnglishName(), ChineseName: Supplier_Gekko.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Snyxis), Name: Supplier_Snyxis.GetEnglishName(), ChineseName: Supplier_Snyxis.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Hyperguest), Name: Supplier_Hyperguest.GetEnglishName(), ChineseName: Supplier_Hyperguest.GetChineseName(), IsActive: false},
		{ID: int(Supplier_TravelGate), Name: Supplier_TravelGate.GetEnglishName(), ChineseName: Supplier_TravelGate.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Juniper), Name: Supplier_Juniper.GetEnglishName(), ChineseName: Supplier_Juniper.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Giata), Name: Supplier_Giata.GetEnglishName(), ChineseName: Supplier_Giata.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Netstorming), Name: Supplier_Netstorming.GetEnglishName(), ChineseName: Supplier_Netstorming.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Tourmind), Name: Supplier_Tourmind.GetEnglishName(), ChineseName: Supplier_Tourmind.GetChineseName(), IsActive: false},
		{ID: int(Supplier_Olivier), Name: Supplier_Olivier.GetEnglishName(), ChineseName: Supplier_Olivier.GetChineseName(), IsActive: true},
		{ID: int(Supplier_Simulator), Name: Supplier_Simulator.GetEnglishName(), ChineseName: Supplier_Simulator.GetChineseName(), IsActive: true}, // For testing
	}
}

// SupplierInfo contains supplier information for API responses
type SupplierInfo struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`        // English name
	ChineseName string `json:"chineseName"` // Chinese name
	IsActive    bool   `json:"isActive"`
}

// Direct connectivity
// TBO
// DOTW
// Hotelbeds
// Expedia
// Trip/ctrip
// Dida
// Almosafer
// Agoda
// Yalago
// Emerging
// Miki
// SmyRoom
// WithinEarth
// Gekko
// Platform connectivity to get multiple suppliers:
//
// Derbysoft (Marriott, Accor)
// Snyxis (Jumeirah, Shangrila)
// Hyperguest (dynamic rates indvidual hotels multiple)
// TravelGate (500 supplier hub)
// Juniper (w2m, Egypt express)
const (
	Supplier_UNKNOWN     Supplier = 0
	Supplier_TBO         Supplier = 1
	Supplier_DOTW        Supplier = 2
	Supplier_Hotelbeds   Supplier = 3
	Supplier_Expedia     Supplier = 4
	Supplier_Trip        Supplier = 5
	Supplier_Dida        Supplier = 6
	Supplier_Almosafer   Supplier = 7
	Supplier_Agoda       Supplier = 8
	Supplier_YalagoSu    Supplier = 9
	Supplier_Emerging    Supplier = 10
	Supplier_Miki        Supplier = 11
	Supplier_SmyRoom     Supplier = 12
	Supplier_WithinEarth Supplier = 13
	Supplier_Gekko       Supplier = 14
	Supplier_Snyxis      Supplier = 15
	Supplier_Hyperguest  Supplier = 16
	Supplier_TravelGate  Supplier = 17
	Supplier_Juniper     Supplier = 18
	Supplier_Giata       Supplier = 19
	Supplier_Netstorming Supplier = 20
	Supplier_Derbysoft   Supplier = 21
	Supplier_Tourmind    Supplier = 22
	Supplier_Olivier     Supplier = 10000001 // 新增 Olivier 房间匹配供应商
	Supplier_Simulator   Supplier = 10000000
)

type CancelType int

const (
	CancelTypeNone    CancelType = iota
	CancelTypeAll     CancelType = 1
	CancelTypePartial CancelType = 2
)

type CancelPolicyType int

const (
	DeductionType_Free CancelPolicyType = iota + 1
	DeductionType_TimeLimit
	DeductionType_Ladder
	DeductionType_Guarantee
	DeductionType_CanNotCancel
	DeductionType_Unknown
)

func (t CancelPolicyType) String() string {
	switch t {
	case DeductionType_Guarantee:
		return "Guarantee"
	case DeductionType_TimeLimit:
		return "TimeLimit"
	case DeductionType_Free:
		return "Free"
	case DeductionType_Ladder:
		return "Ladder"
	case DeductionType_CanNotCancel:
		return "CanNotCancel"
	default:
		return UnknownStr
	}
}

const (
	UnknownStr = "Unknown"
)

const (
	DetailFreeDeductionType  = "FREE"
	AvailFreeDeductionTyp    = "Free"
	AvailLadderDeductionType = "Ladder"
)

type KeywordEnum int64

const (
	KeywordEnum_HOTEL KeywordEnum = 1
	KeywordEnum_GROUP KeywordEnum = 2
	KeywordEnum_BRAND KeywordEnum = 3
)

func (p KeywordEnum) String() string {
	switch p {
	case KeywordEnum_HOTEL:
		return "HOTEL"
	case KeywordEnum_GROUP:
		return "GROUP"
	case KeywordEnum_BRAND:
		return "BRAND"
	default:
		return "<UNSET>"
	}
}

type SearchSortType int64

const (
	ASC  SearchSortType = 1
	DESC SearchSortType = 2
)

func (p SearchSortType) String() string {
	switch p {
	case ASC:
		return "ASC"
	case DESC:
		return "DESC"
	default:
		return "<UNSET>"
	}
}

type SearchSortName int64

const (
	DEFAULT          SearchSortName = 1
	DISTANCE         SearchSortName = 2
	MIN_PRICE        SearchSortName = 3
	CUSTOMER_RATINGS SearchSortName = 4
	STAR             SearchSortName = 5
)

func (p SearchSortName) String() string {
	switch p {
	case DEFAULT:
		return "DEFAULT"
	case DISTANCE:
		return "DISTANCE"
	case MIN_PRICE:
		return "MIN_PRICE"
	case CUSTOMER_RATINGS:
		return "CUSTOMER_RATINGS"
	case STAR:
		return "STAR"
	default:
		return "<UNSET>"
	}
}

const DefaultLocationZoneID string = "Asia/Shanghai"

const DefaultLocationName string = "北京"
