package service

import (
	"context"
	"fmt"
	"hotel/common/bizerr"
	"hotel/common/httpdispatcher"
	"time"

	"hotel/trade/domain"
	"hotel/trade/protocol"

	"hotel/common/log"
)

// Cancel
// @desc: Cancel hotel order, including FULL cancel & PARTIAL cancel
// @tags: openapi,booking.hotelbyte.com/trade
// @path: /cancel
func (s *TradeService) Cancel(ctx context.Context, req *protocol.CancelReq) error {
	startTime := time.Now()

	// 记录取消请求
	user := httpdispatcher.CtxGetUser(ctx)
	s.cancelAuditor.RecordCancelRequest(ctx, req.OrderId, user.ID, "User requested cancellation")

	// 查询订单
	order, err := s.orderDao.GetOrder(ctx, req.OrderId)
	if err != nil {
		return bizerr.NotFoundErr.WithMessagef("order_not_found", req.OrderId)
	}

	// 创建状态机进行状态验证
	currentStatus := domain.OrderStatus(order.Status)
	sm, err := domain.NewOrderStateMachine(currentStatus)
	if err != nil {
		log.Errorc(ctx, "Failed to create state machine for order %d: %v", req.OrderId, err)
		return fmt.Errorf("invalid order state: %w", err)
	}

	// 检查是否可以转换到NeedCancel状态
	if !sm.CanTransitionTo(domain.OrderStateNeedCancel) {
		log.Warnc(ctx, "Order %d cannot be cancelled in current state: %s", req.OrderId, currentStatus.String())
		return fmt.Errorf("order cannot be cancelled in current state: %s", currentStatus.String())
	}

	// 执行状态转换
	if err := sm.TransitionToWithReason(domain.OrderStateNeedCancel, "User requested cancellation"); err != nil {
		log.Errorc(ctx, "Failed to transition order %d state: %v", req.OrderId, err)
		return fmt.Errorf("failed to update order state: %w", err)
	}

	// 更新订单状态到数据库
	order.Status = domain.OrderStateNeedCancel
	if err = s.orderDao.Order.UpdateStatus(ctx, order.Id, order.Status); err != nil {
		log.Errorc(ctx, "CancelOrderFailed: order=%+v, UpdateError=%+v", order, err)
		return fmt.Errorf("failed to update order status: %w", err)
	}

	// 发布取消消息到CQRS队列
	if sendErr := s.cqrsProducer.Publish(ctx, CancelTopic, order); sendErr != nil {
		log.Errorc(ctx, "CancelOrderFailed: order=%+v, PubOrderError=%+v", order, sendErr)
		// 消息发布失败，需要回滚订单状态
		if rollbackErr := s.rollbackOrderStatus(ctx, order, currentStatus); rollbackErr != nil {
			log.Errorc(ctx, "Failed to rollback order %d status: %v", req.OrderId, rollbackErr)
		}

		// 记录失败审计
		processingTime := time.Since(startTime).Milliseconds()
		s.cancelAuditor.RecordCancelFailure(ctx, req.OrderId, currentStatus, "message_queue", sendErr.Error(), processingTime)

		return fmt.Errorf("failed to publish cancel message: %w", sendErr)
	}

	// 记录成功审计
	processingTime := time.Since(startTime).Milliseconds()
	s.cancelAuditor.RecordCancelSuccess(ctx, order, currentStatus, true, processingTime)

	log.Infoc(ctx, "Order %d cancel request processed successfully", req.OrderId)
	return nil
}

// rollbackOrderStatus 回滚订单状态
func (s *TradeService) rollbackOrderStatus(ctx context.Context, order *domain.Order, originalStatus domain.OrderStatus) error {
	order.Status = originalStatus
	if err := s.orderDao.Order.UpdateStatus(ctx, order.Id, originalStatus); err != nil {
		return fmt.Errorf("failed to rollback order status: %w", err)
	}
	log.Infoc(ctx, "Order %d status rolled back to %s", order.Id, originalStatus.String())
	return nil
}
