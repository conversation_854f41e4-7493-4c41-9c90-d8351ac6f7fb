package service

import (
	"context"
	"fmt"
	"strings"
	"sync"

	pkgerr "github.com/pkg/errors"
	"github.com/spf13/cast"

	"hotel/common/bizerr"
	commonDomain "hotel/common/domain"
	"hotel/common/errgroup"
	"hotel/common/linktech"
	"hotel/common/log"
	common "hotel/common/protocol"
	"hotel/common/types"
	"hotel/common/utils"
	contentDomain "hotel/content/domain"
	contentProto "hotel/content/protocol"
	"hotel/search/protocol"
	supplierDomain "hotel/supplier/domain"
	"hotel/user/domain"
)

// HotelRates
// @tags: openapi,Resource,booking.hotelbyte.com/resource
// @path: /hotelRates
// @auth: required
func (s *SearchService) HotelRates(ctx context.Context, req *protocol.HotelRatesReq) (*protocol.HotelRatesResp, error) {
	// 1. 参数检查
	if err := s.validateHotelRatesReq(req); err != nil {
		return nil, err
	}

	// 2. 获取酒店信息
	h, err := s.mustGetHotel(ctx, req.HotelId)
	if err != nil {
		return nil, err
	}

	// 3. 获取买家和卖家
	buyer, sellers, err := s.getBuyerAndSellers(ctx, req)
	if err != nil {
		return nil, err
	}

	// 4. 获取/创建 Session
	session, err := s.sessionService.GetOrCreateSession(ctx, req.SessionId)
	if err != nil {
		return nil, err
	}
	sid := session.Id

	// 5. 并发请求所有卖家
	supplierRatesRespList, sellerPayloads, err := s.fetchAllSupplierRates(ctx, sellers, h, req, session)
	if err != nil {
		return nil, err
	}
	session.SetSellerPayloads(ctx, utils.ToJSON(sellerPayloads))

	// 5.5. 编码 RatePkgId
	s.encodeRatePkgIds(supplierRatesRespList)

	// 6. 合并房型
	rooms := s.mergeSupplierHotelRatesResp(supplierRatesRespList)

	// 7. 集成房型映射
	rooms = s.integrateRoomMapping(ctx, req.CountryOption.CountryCode, rooms)

	// 8. 买家出参规则处理
	rooms, err = s.applyBuyerOutRule(ctx, buyer, rooms)
	if err != nil {
		return nil, err
	}

	// 9. 缓存 Session
	if err = s.sessionService.CacheSession(ctx, sid, session); err != nil {
		return nil, err
	}

	// 10. 构造返回值
	return &protocol.HotelRatesResp{
		Rooms: rooms,
		ResponseHeader: common.ResponseHeader{
			SessionOption: common.SessionOption{
				SessionId: sid,
			},
		},
	}, nil
}

// validateHotelRatesReq 校验 HotelRates 请求参数
func (s *SearchService) validateHotelRatesReq(req *protocol.HotelRatesReq) error {
	if req == nil {
		return pkgerr.New("HotelRatesReq is nil")
	}
	if req.HotelId == 0 {
		return pkgerr.New("HotelId is required")
	}
	if req.CheckIn == 0 || req.CheckOut == 0 {
		return pkgerr.New("CheckIn/CheckOut is required")
	}
	if req.RoomCount == 0 || req.AdultCount == 0 {
		return pkgerr.New("RoomCount/AdultCount is required")
	}
	return nil
}

// getBuyerAndSellers 获取买家和卖家信息
func (s *SearchService) getBuyerAndSellers(ctx context.Context, req *protocol.HotelRatesReq) (buyer linktech.BuyerResolver, sellers []linktech.Participant, err error) {
	buyer = s.userSrv.GetBuyerResolver(ctx)
	sellers, err = buyer.Sellers(ctx)
	if err != nil {
		err = pkgerr.Wrapf(err, "buyer.Sellers")
		return
	}
	return
}

// fetchAllSupplierRates 并发请求所有卖家
func (s *SearchService) fetchAllSupplierRates(ctx context.Context, sellers []linktech.Participant, h *contentDomain.Hotel, req *protocol.HotelRatesReq, session *commonDomain.Session) (supplierRatesRespList []*supplierDomain.HotelRatesResp, sellerPayloads supplierDomain.SellerPayloadList, err error) {
	supplierRatesRespList = make([]*supplierDomain.HotelRatesResp, 0)
	sellerPayloads = make(supplierDomain.SellerPayloadList, 0, len(sellers))
	var (
		mu sync.Mutex
	)
	wg := errgroup.WithContext(ctx)
	for _, seller := range sellers {
		seller := seller // capture range variable
		wg.Go(func(ctx context.Context) error {
			var (
				ctxPayload  = supplierDomain.BaseRequestContextPayload{}
				supplierReq = &supplierDomain.HotelRatesReq{
					SupplierHotelId: "",
					HotelRegion:     supplierDomain.HotelRegion{},
					CheckInOut:      req.CheckInOut,
					GuestRoomOption: req.GuestRoomOption,
					CurrencyOption:  req.CurrencyOption,
				}
			)

			// sellerInRule
			sellerInRuleId, ruleErr := seller.InNode.GetAggregatedRuleId(ctx)
			if ruleErr != nil {
				return pkgerr.Wrapf(ruleErr, "get sellerInRule %v", seller.EntityId)
			}
			sellerInRule, ruleErr := s.ruleSrv.GetRule(ctx, sellerInRuleId)
			if ruleErr != nil {
				return pkgerr.Wrapf(ruleErr, "get sellerInRule %v", sellerInRuleId)
			}
			ruleRes, ruleErr := sellerInRule.RunWithMap(ctx, map[string]interface{}{
				"supplierEntityId": seller.EntityId,
			})
			if ruleErr != nil {
				return pkgerr.Wrapf(ruleErr, "Run sellerInRule %v", sellerInRuleId)
			}
			switch ruleRes.Aim().AsAction().ActionName() {
			default:
				paramsMap := ruleRes.Aim().AsAction().ParamAsMap()
				supplier := paramsMap["supplier"]
				if supplier != nil {
					ctxPayload.Supplier = domain.SupplierEntityId2Supplier(types.ID(cast.ToInt64(supplier)))
					supplierReq.SupplierHotelId = h.GetSupplierHotelId(ctxPayload.Supplier)
				} else {
					ctxPayload.Supplier = domain.SupplierEntityId2Supplier(seller.EntityId)
					supplierReq.SupplierHotelId = h.GetSupplierHotelId(ctxPayload.Supplier)
				}
			}
			if supplierReq.SupplierHotelId == "" {
				log.Infoc(ctx, "supplier %v not found for hotel %v", ctxPayload.Supplier, h.ID)
				return nil
			}
			credentialId, credErr := seller.InNode.GetCredentialId(ctx)
			if credErr != nil {
				return pkgerr.Wrapf(credErr, "get sellerInRule GetCredentialId %v", seller.EntityId)
			}
			sp, credErr := s.userSrv.GetSupplierCredential(ctx, credentialId)
			if credErr != nil {
				return pkgerr.Wrapf(credErr, "get supplier credential %d", credentialId)
			}
			ctxPayload.Properties = sp.CredentialContent
			ctxPayload.Session = session
			mu.Lock()
			sellerPayloads = append(sellerPayloads, &supplierDomain.SellerInputPayloads{
				Supplier:   ctxPayload.Supplier,
				Properties: ctxPayload.Properties.(string),
				HotelRates: supplierReq,
			})
			mu.Unlock()

			ctx = supplierDomain.InjectBaseRequestContextPayload(ctx, ctxPayload)
			resp, respErr := s.supplierFact.HotelRates(ctx, supplierReq)
			if respErr != nil {
				return pkgerr.Wrapf(respErr, "supplier %v HotelRates", ctxPayload.Supplier)
			}

			sellerOutRuleId, ruleErr := seller.OutNode.GetAggregatedRuleId(ctx)
			if ruleErr != nil {
				return pkgerr.Wrapf(ruleErr, "get sellerOutRule %v", seller.EntityId)
			}
			sellerOutRule, ruleErr := s.ruleSrv.GetRule(ctx, sellerOutRuleId)
			if ruleErr != nil {
				return pkgerr.Wrapf(ruleErr, "get sellerOutRule %v", sellerOutRuleId)
			}
			ruleRes, ruleErr = sellerOutRule.RunWithMap(ctx, map[string]interface{}{
				"supplierResp": resp,
				"masterHotel":  h,
				"ctxPayload":   ctxPayload,
			})
			if ruleErr != nil {
				return pkgerr.Wrapf(ruleErr, "Run sellerOutRule %v", sellerOutRuleId)
			}
			log.Infoc(ctx, "sellerOutRule ruleRes(%#v)", ruleRes)
			mu.Lock()
			supplierRatesRespList = append(supplierRatesRespList, resp)
			mu.Unlock()
			return nil
		})
	}
	if err = wg.Wait(); err != nil {

	}
	return
}

// applyBuyerOutRule 买家出参规则处理
func (s *SearchService) applyBuyerOutRule(ctx context.Context, buyer linktech.BuyerResolver, rooms []supplierDomain.Room) ([]supplierDomain.Room, error) {
	buyerOutRuleId, err := buyer.Buyer().OutNode.GetAggregatedRuleId(ctx)
	if err != nil {
		return nil, pkgerr.Wrapf(err, "get buyer outRule %v", buyerOutRuleId)
	}
	buyerOutRule, err := s.ruleSrv.GetRule(ctx, buyerOutRuleId)
	if err != nil {
		return nil, pkgerr.Wrapf(err, "get buyerOutRule %v", buyerOutRuleId)
	}
	buyerOutRuleRes, err := buyerOutRule.RunWithMap(ctx, map[string]interface{}{
		"rooms": rooms,
	})
	if err != nil {
		return nil, pkgerr.Wrapf(err, "Run buyerOutRule %v", buyerOutRuleId)
	}
	log.Infoc(ctx, "buyerOutRuleRes %#v", buyerOutRuleRes)
	// todo: 动态修改 rooms 的能力可后续补充
	return rooms, nil
}

func getEncodedRatePkgId(sup supplierDomain.Supplier, ratePkgId string) string {
	return fmt.Sprintf("%d:%s", sup.Int64(), ratePkgId)
}
func getDecodedRatePkgId(encodedRatePkgId string) (sup supplierDomain.Supplier, ratePkgId string) {
	parts := strings.Split(encodedRatePkgId, ":")
	if len(parts) != 2 {
		return 0, ""
	}
	return supplierDomain.Supplier(cast.ToInt64(parts[0])), parts[1]
}

// todo: integrate room-mapping
func (s *SearchService) encodeRatePkgIds(a []*supplierDomain.HotelRatesResp) {
	for _, v := range a {
		sup := v.Supplier
		for _, r := range v.Rooms {
			for i := range r.Rates {
				r.Rates[i].RatePkgId = getEncodedRatePkgId(sup, r.Rates[i].RatePkgId)
			}
		}
	}
}

// Enhanced room merging with mapping-aware deduplication
func (s *SearchService) mergeSupplierHotelRatesResp(supplierRespList []*supplierDomain.HotelRatesResp) (out []supplierDomain.Room) {
	// Step 1: Collect all rooms with encoded RatePkgId
	allRooms := make([]supplierDomain.Room, 0)
	for _, v := range supplierRespList {
		sup := v.Supplier
		for _, r := range v.Rooms {
			// Create a copy of the room to avoid modifying original
			roomCopy := r
			for i := range roomCopy.Rates {
				roomCopy.Rates[i].RatePkgId = getEncodedRatePkgId(sup, roomCopy.Rates[i].RatePkgId)
			}
			allRooms = append(allRooms, roomCopy)
		}
	}
	
	// Step 2: Group similar rooms for potential merging
	// This will be enhanced after room mapping integration
	roomGroups := s.groupSimilarRooms(allRooms)
	
	// Step 3: Apply merging strategy
	for _, group := range roomGroups {
		if len(group) == 1 {
			// Single room, add directly
			out = append(out, group[0])
		} else {
			// Multiple similar rooms, merge or keep separate based on strategy
			merged := s.mergeSimilarRooms(group)
			out = append(out, merged...)
		}
	}
	
	return out
}

// groupSimilarRooms groups rooms that might represent the same room type
func (s *SearchService) groupSimilarRooms(rooms []supplierDomain.Room) [][]supplierDomain.Room {
	// Simple grouping by room name for now
	// After mapping integration, this will use mapped room IDs
	groups := make(map[string][]supplierDomain.Room)
	
	for _, room := range rooms {
		key := room.RoomName.En
		if key == "" {
			key = room.RoomTypeID
		}
		groups[key] = append(groups[key], room)
	}
	
	result := make([][]supplierDomain.Room, 0, len(groups))
	for _, group := range groups {
		result = append(result, group)
	}
	return result
}

// mergeSimilarRooms decides how to handle similar rooms from different suppliers
func (s *SearchService) mergeSimilarRooms(similarRooms []supplierDomain.Room) []supplierDomain.Room {
	// Strategy 1: Keep all rooms separate to show all supplier options
	// Strategy 2: Merge rooms and combine rates
	// For now, using Strategy 1 - keep all rooms
	
	// Future enhancement: Use mapping confidence scores to decide merging strategy
	return similarRooms
}
func (s *SearchService) mustGetHotel(ctx context.Context, hid types.ID) (*contentDomain.Hotel, error) {
	hResp, err := s.contentSrv.GetHotel(ctx, &contentProto.GetHotelReq{HotelId: hid})
	if err != nil {
		return nil, pkgerr.Wrap(err, "GetHotel")
	}
	h := hResp.Hotel
	if h == nil {
		return nil, bizerr.NotFoundErr.WithMessagef("Hotel not found by %v", hid)
	}
	return h, nil
}
