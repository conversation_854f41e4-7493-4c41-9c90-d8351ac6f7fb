MySQL:
  # // 示例DSN配置
  #dsn := "user:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=UTC&timeout=10s&readTimeout=30s&writeTimeout=30s"
  User: admin:Amit2025@tcp(hoteltest.chayosky20ji.ap-southeast-1.rds.amazonaws.com:3306)/hoteltest?timeout=10s&readTimeout=30s&writeTimeout=30s&charset=utf8mb4&parseTime=True&loc=UTC
Redis:
  Host: "localhost:6379"
  Type: "node"
  Pass: ""
  DB: 0