# 定义变量
PROJECT_PATH := ${PWD}

# 文档相关命令
doc:
	@echo "Updating documentation"
	go run ./build/api

doc-upload:
	@echo "Updating & Uploading documentation "
# 使用 sh -c 明确分隔环境变量和命令
	sh -c 'UPLOAD=true go run ./build/api'

# 初始化开发环境
init-dev:
	echo "export PROJECT_PATH=\"$(PROJECT_PATH)\"" >> ~/.zshrc && zsh && source ~/.zshrc

# 初始化开发环境（首次设置）
setup-dev:
	@echo "Setting up development environment (first time setup)"
	sh ./build/dev/setup.sh

# 启动基础设施服务（MySQL、Redis等）
start-infra:
	@echo "Starting infrastructure services (MySQL, Redis, etc.)"
	docker-compose -f build/dev/docker-compose.yml up -d

# 停止基础设施服务
stop-infra:
	@echo "Stopping infrastructure services"
	docker-compose -f build/dev/docker-compose.yml down

# 重启基础设施服务
restart-infra:
	@echo "Restarting infrastructure services"
	docker-compose -f build/dev/docker-compose.yml restart

# 查看基础设施服务状态
status-infra:
	@echo "Infrastructure services status:"
	docker-compose -f build/dev/docker-compose.yml ps

# 运行应用（开发模式）
run-dev:
	@echo "Running application in development mode"
	go run api/api.go

# 运行应用（生产模式）
run:
	@echo "Running application in production mode"
	./goweb.app

# 编译应用
build:
	@echo "Compiling application"
	go build -o goweb.app api/api.go

# 构建Docker镜像
build-image:
	@echo "Building Docker image"
	sh build/build_image.sh

# 生成API文档和DAO模型
dao:
	@echo "Building API docs + Dao models"
	sh ./build/build.sh

# 生成数据库DDL
ddl:
	@echo "Generating database DDL"
	go run build/ddl/migration_prepare.go
	sh build/ddl/migration.sh

# 运行测试
test:
	@echo "Running tests"
	go test -v ./...

# 运行测试并显示覆盖率
test-coverage:
	@echo "Running tests with coverage"
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 运行特定包的测试
test-package:
	@echo "Usage: make test-package PKG=package_name"
	@if [ -z "$(PKG)" ]; then \
		echo "Please specify package name: make test-package PKG=content/mysql"; \
		exit 1; \
	fi
	go test -v ./$(PKG)

# 代码检查
lint:
	@echo "Running code linting"
	golangci-lint run

# 清理构建文件
clean:
	@echo "Cleaning build files"
	rm -f goweb.app
	rm -f coverage.out
	rm -f coverage.html

# 显示帮助信息
help:
	@echo "Available commands:"
	@echo "  setup-dev      - Set up development environment (first time only)"
	@echo "  start-infra    - Start infrastructure services (MySQL, Redis, etc.)"
	@echo "  stop-infra     - Stop infrastructure services"
	@echo "  restart-infra  - Restart infrastructure services"
	@echo "  status-infra   - Show infrastructure services status"
	@echo "  run-dev        - Run application in development mode"
	@echo "  run            - Run application in production mode"
	@echo "  build          - Build application"
	@echo "  test           - Run all tests"
	@echo "  test-coverage  - Run tests with coverage report"
	@echo "  test-package   - Run tests for specific package (PKG=package_name)"
	@echo "  lint           - Run code linting"
	@echo "  clean          - Clean build files"
	@echo "  ddl            - Generate database DDL"
	@echo "  dao            - Generate API docs and DAO models"
	@echo "  build-image    - Build Docker image"
	@echo "  doc            - Update documentation"
	@echo "  help           - Show this help message"