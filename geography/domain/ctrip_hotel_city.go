package domain

import (
	"fmt"

	"hotel/common/idgen"
	"hotel/common/types"
	"hotel/common/utils"

	"github.com/spf13/cast"
)

type DeprecatedBDCtripHotelCity struct {
	ID            int64  `json:"id"`
	RegionCode    string `json:"regionCode"`
	CountryId     int64  `json:"countryId"`
	ProvinceId    int64  `json:"provinceId"`
	CityName      string `json:"cityName"`
	CityNameEn    string `json:"cityNameEn"`
	CountryName   string `json:"countryName"`
	CountryEName  string `json:"countryEname"`
	JianPin       string `json:"jianPin"`
	ProvinceName  string `json:"provinceName"`
	ProvinceEName string `json:"provinceEname"`
}

func (c *DeprecatedBDCtripHotelCity) FromStringMap(in map[string]string) *DeprecatedBDCtripHotelCity {
	v, _ := c.FromStringMapE(in)
	return v
}

func (c *DeprecatedBDCtripHotelCity) FromStringMapE(in map[string]string) (*DeprecatedBDCtripHotelCity, error) {
	v, err := utils.MapToStruct[DeprecatedBDCtripHotelCity](in)
	if err != nil {
		return nil, err
	}
	return v, nil
}

// {"countryId":98,"name":"新西兰","enName":"New Zealand","code":"NZ","continentId":3,"continentName":"大洋洲","areaCode":"64"}
func (c *CtripPOIData) ToRegion(countryRegion *Region) *Region {
	cities := make([]*Region, 0)
	for _, city := range c.PrefectureLevelCityInfoList {
		counties := make([]*Region, 0)
		for _, county := range city.CountyList {
			region := &Region{
				ID:       idgen.String2Int64ID(cast.ToString(county.CountyID)),
				Type:     RegionType_City,
				Name:     county.CountyEnName,
				NameFull: fmt.Sprintf("%s,%s,%s,%s", county.CountyEnName, city.CityEnName, c.ProvinceEnName, countryRegion.Name),
				SupplierID: SupplierID{
					TripId: cast.ToString(county.CountyID),
				},
				CountryCode:            countryRegion.CountryCode,
				CountrySubdivisionCode: countryRegion.CountrySubdivisionCode,
				Coordinates:            Coordinates{},
				Ancestors:              nil,
				Descendants:            nil,
				Extra: Extra{
					NameZh:        county.CountyName,
					CityName:      city.CityEnName,
					ProvinceName:  c.ProvinceEnName,
					CountryName:   countryRegion.Name,
					CountryNameZh: countryRegion.Extra.NameZh,
				},
			}
			// 在数据构建时优化NameFull
			region.OptimizeNameFull()
			counties = append(counties, region)
		}
		cityRegion := &Region{
			ID:       idgen.String2Int64ID(cast.ToString(city.CityID)),
			Type:     RegionType_MultiCityVicinity,
			Name:     city.CityEnName,
			NameFull: fmt.Sprintf("%s,%s,%s", city.CityEnName, c.ProvinceEnName, countryRegion.Name),
			SupplierID: SupplierID{
				TripId: cast.ToString(city.CityID),
			},
			CountryCode:            countryRegion.CountryCode,
			CountrySubdivisionCode: countryRegion.CountrySubdivisionCode,
			Coordinates:            Coordinates{},
			Ancestors:              nil,
			Descendants:            counties,
			Extra: Extra{
				NameZh:        city.CityName,
				ProvinceName:  c.ProvinceEnName,
				CountryName:   countryRegion.Name,
				CountryNameZh: countryRegion.Extra.NameZh,
			},
		}
		// 在数据构建时优化NameFull
		cityRegion.OptimizeNameFull()
		cities = append(cities, cityRegion)
	}
	provinceRegion := &Region{
		ID:       types.ID(CtripProvinceID2RegionID(c.ProvinceID)),
		Type:     RegionType_ProvinceState,
		Name:     c.ProvinceEnName,
		NameFull: fmt.Sprintf("%s,%s", c.ProvinceEnName, countryRegion.Name),
		SupplierID: SupplierID{
			TripId: cast.ToString(c.ProvinceID),
		},
		CountryCode:            countryRegion.CountryCode,
		CountrySubdivisionCode: countryRegion.CountrySubdivisionCode,
		Coordinates:            Coordinates{},
		Descendants:            cities,
		Extra: Extra{
			NameZh:        c.ProvinceName,
			ProvinceName:  c.ProvinceEnName,
			CountryName:   countryRegion.Name,
			CountryNameZh: countryRegion.Extra.NameZh,
		},
	}
	// 在数据构建时优化NameFull
	provinceRegion.OptimizeNameFull()
	return provinceRegion
}
func (c *CtripCountryBaseInfo) ToRegion() *Region {
	return &Region{
		ID:       types.ID(CtripCountryID2RegionID(c.CountryId)),
		Type:     RegionType_Country,
		Name:     c.EnName,
		NameFull: c.EnName,
		SupplierID: SupplierID{
			TripId: cast.ToString(c.CountryId),
		},
		CountryCode:            c.Code,
		CountrySubdivisionCode: "",
		Coordinates:            Coordinates{},
		Ancestors:              nil,
		Descendants:            nil,
		Extra: Extra{
			NameZh:          c.Name,
			ContinentNameZh: c.ContinentName,
		},
	}
}

type DeprecatedCtripHotelCity struct {
	City          int64 // SupplierHotelId
	CountryName   string
	CountryEName  string
	CityName      string
	CityEName     string
	Country       int64 // SupplierHotelId
	JianPin       string
	Province      int64 // SupplierHotelId
	ProvinceName  string
	ProvinceEName string
}

func (c *DeprecatedCtripHotelCity) FromDeprecatedBDCtripHotelCityStringMap(in map[string]string) *DeprecatedCtripHotelCity {
	v := (&DeprecatedBDCtripHotelCity{}).FromStringMap(in)
	return &DeprecatedCtripHotelCity{
		City:          cast.ToInt64(v.ID),
		CountryName:   v.CountryName,
		CountryEName:  v.CountryEName,
		CityName:      v.CityName,
		CityEName:     v.CityNameEn,
		Country:       v.CountryId,
		JianPin:       v.JianPin,
		Province:      v.ProvinceId,
		ProvinceName:  v.ProvinceName,
		ProvinceEName: v.ProvinceEName,
	}
}
func (c *DeprecatedCtripHotelCity) FromStringMap(in map[string]string) *DeprecatedCtripHotelCity {
	v, _ := c.FromStringMapE(in)
	return v
}

func (c *DeprecatedCtripHotelCity) FromStringMapE(in map[string]string) (*DeprecatedCtripHotelCity, error) {
	v, err := utils.MapToStruct[DeprecatedCtripHotelCity](in)
	if err != nil {
		return nil, err
	}
	return v, nil
}

// ctrip的city、province、country没有共用一套ID，需要强行映射一波
func CtripCountryID2RegionID(v int64) int64 {
	return 1000000 + v
}

// ctrip的city、province、country没有共用一套ID，需要强行映射一波
func CtripProvinceID2RegionID(v int64) int64 {
	return 2000000 + v
}

func (c *DeprecatedCtripHotelCity) ToRegion() *Region {
	if c == nil {
		return nil
	}
	countryRegionID := CtripCountryID2RegionID(c.Country)
	provinceRegionID := CtripProvinceID2RegionID(c.Province)
	country := &Region{
		ID:       types.ID(countryRegionID),
		Type:     RegionType_Country,
		Name:     c.CountryEName,
		NameFull: fmt.Sprintf("%s", c.CountryEName),
		SupplierID: SupplierID{
			TripId: cast.ToString(c.Country),
		},
		CountryCode:            "",
		CountrySubdivisionCode: "",
		Coordinates:            Coordinates{},
		Ancestors:              nil,
		Descendants:            nil,
		Extra: Extra{
			NameZh: c.CountryName,
		},
	}

	province := &Region{
		ID:       types.ID(provinceRegionID),
		Type:     RegionType_ProvinceState,
		Name:     c.ProvinceEName,
		NameFull: fmt.Sprintf("%s,%s", c.ProvinceEName, c.CountryEName),
		SupplierID: SupplierID{
			TripId: cast.ToString(c.Province),
		},
		CountryCode:            "",
		CountrySubdivisionCode: "",
		Coordinates:            Coordinates{},
		Ancestors: []*Region{
			country,
		},
		Descendants: nil,
		Extra: Extra{
			NameZh:      c.ProvinceName,
			CountryName: c.CountryEName,
		},
	}
	if province.ID.Int64() == c.City {
		province = nil // reset it to nil
	}

	return &Region{
		ID:   idgen.String2Int64ID(cast.ToString(c.City)),
		Type: RegionType_MultiCityVicinity,
		Name: c.CityEName,
		NameFull: func() string {
			if c.CityEName == c.ProvinceEName {
				return fmt.Sprintf("%s,%s", c.CityEName, c.CountryEName)
			}
			return fmt.Sprintf("%s,%s,%s", c.CityEName, c.ProvinceEName, c.CountryEName)
		}(),
		SupplierID: SupplierID{
			TripId: cast.ToString(c.City),
		},
		CountryCode:            "", // todo
		CountrySubdivisionCode: "", // todo
		Coordinates:            Coordinates{},
		Ancestors: []*Region{
			province,
			country,
		},
		Descendants: nil,
		Extra: Extra{
			NameZh:       c.CityName,
			CountryName:  c.CountryEName,
			ProvinceName: c.ProvinceEName,
		},
	}
}
