package service

import (
	"context"
	"strconv"

	"hotel/trade/protocol"
)

// LabelOrder
// @desc: 订单打标签
// @tags: admin.hotelbyte.com
func (s *TradeService) LabelOrder(ctx context.Context, req *protocol.LabelOrderReq) (*protocol.LabelOrderResp, error) {
	orderID, err := strconv.ParseUint(req.OrderId, 10, 64)
	if err != nil {
		return nil, err
	}
	
	_, err = s.orderDao.Order.FindOne(ctx, orderID)
	if err != nil {
		return nil, err
	}

	return &protocol.LabelOrderResp{}, nil
}
