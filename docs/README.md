# 文档中心

欢迎来到 Hotel-BE 项目文档中心！这里包含了项目的完整技术文档，帮助您快速了解和使用我们的酒店分销平台。

## 📚 文档导航

### 🚀 快速开始

- [项目概述](../README.md) - 项目介绍和核心特性
- [环境搭建](../README.md#环境搭建) - 开发环境配置
- [快速启动](../README.md#快速启动) - 项目启动指南

### 🏗️ 架构设计

- [系统架构](architecture/README.md) - 整体架构设计
- [模块设计](modules/README.md) - 各模块详细设计
- [API文档](api/README.md) - 接口规范文档

### 🔧 开发指南

- [编码规范](development/coding-standards.md) - 代码规范和最佳实践
- [测试指南](development/testing-guide.md) - 测试策略和工具
- [部署指南](development/deployment-guide.md) - 部署流程和环境管理

### 📊 性能优化

- [性能优化记录](performance/README.md) - 性能优化历史记录
- [数据库优化](performance/database-optimization.md) - 数据库性能优化
- [缓存策略](performance/cache-strategy.md) - 缓存设计和实现

### 🛠️ 运维管理

- [监控告警](operations/monitoring.md) - 系统监控和告警配置
- [日志管理](operations/logging.md) - 日志收集和分析
- [故障排查](operations/troubleshooting.md) - 常见问题解决方案

### 📋 业务模块

- [用户管理](modules/user/README.md) - 用户中心模块
- [订单管理](modules/trade/README.md) - 订单中心模块
- [供应商集成](modules/supplier/README.md) - 供应商管理模块
- [内容管理](modules/content/README.md) - 内容管理模块


## 🔍 文档搜索

### 按关键词搜索

- **性能优化**: [性能优化记录](performance/README.md)
- **数据库**: [数据库优化](performance/database-optimization.md)
- **API**: [API文档](api/README.md)
- **测试**: [测试指南](development/testing-guide.md)
- **部署**: [部署指南](development/deployment-guide.md)

### 按问题类型搜索

- **环境配置问题**: [环境搭建](../README.md#环境搭建)
- **性能问题**: [性能优化记录](performance/README.md)
- **API使用问题**: [API文档](api/README.md)
- **部署问题**: [部署指南](development/deployment-guide.md)

## 📝 文档贡献

我们欢迎社区贡献文档！如果您发现文档有错误或需要补充，请：

1. **创建Issue**: 在GitHub上创建Issue描述问题
2. **提交PR**: 创建Pull Request提交修改
3. **遵循规范**: 请遵循我们的[文档编写规范](development/documentation-standards.md)

### 文档编写规范

- **结构清晰**: 使用清晰的标题层级
- **内容准确**: 确保技术内容的准确性
- **示例完整**: 提供完整的代码示例
- **更新及时**: 及时更新过时的文档

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. **查看文档**: 首先查看相关文档
2. **搜索Issues**: 在GitHub Issues中搜索类似问题
3. **创建Issue**: 如果问题仍未解决，请创建新的Issue
4. **联系维护者**: 对于紧急问题，请联系项目维护者

## 🔄 文档更新日志

### 2025-07-23

- 重新组织文档结构，提升可读性和可搜索性
- 合并冗余文档，优化文档导航
- 添加性能优化相关文档

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队
