package service

import (
	"context"
	"github.com/bytedance/mockey"
	"hotel/mapping/supplier/giata"
	"hotel/mapping/supplier/olivier"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"hotel/common/i18n"
	rmDomain "hotel/mapping/supplier/olivier/domain"
	supplierDomain "hotel/supplier/domain"
)

func TestIntegrateRoomMapping(t *testing.T) {
	ctx := context.Background()

	// Create test service with mock clients
	s := &SearchService{
		olivierClient:   &olivier.OlivierClient{},
		giataRoomClient: &giata.GiataRoomMappingClient{},
	}

	// Test data: rooms from multiple suppliers
	rooms := []supplierDomain.Room{
		{
			HotelRoomStaticProfile: supplierDomain.HotelRoomStaticProfile{
				RoomTypeID:      "deluxe_001",
				SupplierHotelId: "hotel_123",
				RoomName:        i18n.I18N{En: "Deluxe Room"},
			},
			Rates: []supplierDomain.RoomRatePkg{
				{RatePkgId: "1:rate_001"}, // Dida supplier
			},
		},
		{
			HotelRoomStaticProfile: supplierDomain.HotelRoomStaticProfile{
				RoomTypeID:      "dlx_rm_001",
				SupplierHotelId: "hotel_123",
				RoomName:        i18n.I18N{En: "Deluxe Room"},
			},
			Rates: []supplierDomain.RoomRatePkg{
				{RatePkgId: "2:rate_002"}, // Ctrip supplier
			},
		},
	}

	mockey.PatchConvey("", t, func() {
		mockey.Mock((*olivier.OlivierClient).RoomMatching).To((*mockOlivierClient).RoomMatching).Build()
		mockey.Mock((*giata.GiataRoomMappingClient).RoomMatching).To((*mockOlivierClient).RoomMatching).Build()
		result := s.integrateRoomMapping(ctx, "US", rooms)

		// Verify mapping results
		require.Len(t, result, 2)
		assert.NotNil(t, result[0].MappingProcess)
		assert.NotNil(t, result[1].MappingProcess)
		assert.True(t, result[0].MappingProcess.OlivierMapped)
		assert.True(t, result[1].MappingProcess.OlivierMapped)
	})

}

func TestGetSupplierNameFromRatePkgId(t *testing.T) {
	tests := []struct {
		name         string
		encodedId    string
		expectedName string
	}{
		{
			name:         "Dida supplier",
			encodedId:    "1:rate_123",
			expectedName: "Dida",
		},
		{
			name:         "Ctrip supplier",
			encodedId:    "2:rate_456",
			expectedName: "Ctrip",
		},
		{
			name:         "Invalid format",
			encodedId:    "invalid",
			expectedName: "unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getSupplierNameFromRatePkgId(tt.encodedId)
			assert.Equal(t, tt.expectedName, result)
		})
	}
}

func TestMergeSupplierHotelRatesResp(t *testing.T) {
	s := &SearchService{}

	// Test data: responses from multiple suppliers
	supplierRespList := []*supplierDomain.HotelRatesResp{
		{
			Supplier: supplierDomain.Supplier_Dida,
			Rooms: []supplierDomain.Room{
				{
					HotelRoomStaticProfile: supplierDomain.HotelRoomStaticProfile{
						RoomTypeID: "dida_deluxe",
						RoomName:   i18n.I18N{En: "Deluxe Room"},
					},
					Rates: []supplierDomain.RoomRatePkg{
						{RatePkgId: "dida_rate_1"},
					},
				},
			},
		},
		{
			Supplier: supplierDomain.Supplier_Trip,
			Rooms: []supplierDomain.Room{
				{
					HotelRoomStaticProfile: supplierDomain.HotelRoomStaticProfile{
						RoomTypeID: "ctrip_deluxe",
						RoomName:   i18n.I18N{En: "Deluxe Room"},
					},
					Rates: []supplierDomain.RoomRatePkg{
						{RatePkgId: "ctrip_rate_1"},
					},
				},
			},
		},
	}

	result := s.mergeSupplierHotelRatesResp(supplierRespList)

	// Verify merging results
	require.Len(t, result, 2)
	// Verify encoded RatePkgId
	assert.Contains(t, result[0].Rates[0].RatePkgId, ":")
	assert.Contains(t, result[1].Rates[0].RatePkgId, ":")
	// Verify supplier information is preserved
	//assert.Equal(t, supplierDomain.Supplier_Dida, result[0].Rates[0].S)
	//assert.Equal(t, supplierDomain.Supplier_Trip, result[1].Su)
}

// Mock implementations for testing
type mockOlivierClient struct{}

func (m *mockOlivierClient) RoomMatching(ctx context.Context, req *rmDomain.RoomMatchingReq) (*rmDomain.RoomMatchingResp, error) {
	return &rmDomain.RoomMatchingResp{
		Matching: []rmDomain.RoomMatch{
			{
				RoomName: "Deluxe Room",
				Suppliers: []rmDomain.RoomMatchSupplierItem{
					{
						Supplier: "Dida",
						HotelID:  "hotel_123",
						RoomKey: rmDomain.RoomKey{
							RoomID:     "mapped_deluxe_001",
							RoomCode:   "deluxe_001",
							Confidence: 0.9,
						},
					},
					{
						Supplier: "Ctrip",
						HotelID:  "hotel_123",
						RoomKey: rmDomain.RoomKey{
							RoomID:     "mapped_deluxe_001",
							RoomCode:   "dlx_rm_001",
							Confidence: 0.85,
						},
					},
				},
			},
		},
	}, nil
}

type mockGiataClient struct{}

func (m *mockGiataClient) RoomMatching(ctx context.Context, req *rmDomain.RoomMatchingReq) (*rmDomain.RoomMatchingResp, error) {
	return &rmDomain.RoomMatchingResp{
		Matching: []rmDomain.RoomMatch{
			{
				RoomName: "Deluxe Room",
				Suppliers: []rmDomain.RoomMatchSupplierItem{
					{
						Supplier: "Standard",
						HotelID:  "hotel_123",
						RoomKey: rmDomain.RoomKey{
							RoomID:     "giata_standard_deluxe",
							RoomCode:   "mapped_deluxe_001",
							Confidence: 0.95,
						},
					},
				},
			},
		},
	}, nil
}
