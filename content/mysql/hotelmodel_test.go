package mysql_test

import (
	"context"
	"testing"
	"time"

	"hotel/content/mysql"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	geoDomain "hotel/geography/domain"
)

func initHotelModel() *mysql.HotelModel {
	// 启动内存Redis
	//mr, cacheConf := testkit.CreateMiniRedis()
	//defer mr.Close()

	// 使用测试用内存数据库
	conn := sqlx.NewSqlConn("mysql", "root:123456@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=UTC")
	return mysql.NewHotelModel(conn)

}
func TestSRID(t *testing.T) {
	// 准备带坐标的测试数据
	testHotel := &mysql.Hotel{
		GoogleGeo:     geoDomain.NewWGS84GeoPoint(39.90923, 116.397428),
		GaodeGeo:      geoDomain.NewWGS84GeoPoint(39.904300, 116.402500),
		StaticProfile: "{}",
		BrandId:       1,
		Id:            time.Now().UnixNano(), // 使用纳秒级时间戳避免冲突
	}
	hotelModel := initHotelModel()

	// 执行插入
	err := hotelModel.Insert(context.Background(), testHotel)
	assert.NoError(t, err)

	// 验证SRID设置
	t.Run("Check_SRID", func(t *testing.T) {
		err := hotelModel.ValidateSRID(context.Background(), testHotel.Id)
		assert.NoError(t, err)
	})

	// 验证坐标精度
	t.Run("Coordinate_Precision", func(t *testing.T) {
		h, err := hotelModel.FindByID(context.Background(), testHotel.Id)
		assert.NoError(t, err)
		assert.InDelta(t, 116.397428, h.GoogleGeo.Point.Lon(), 0.000001)
		assert.InDelta(t, 39.90923, h.GoogleGeo.Point.Lat(), 0.000001)
	})
}
func TestHotelCRUD(t *testing.T) {
	hotelModel := initHotelModel()

	// 准备测试数据
	testHotel := &mysql.Hotel{
		Id:               time.Now().UnixNano(), // 使用纳秒级时间戳避免冲突
		CityRegionId:     2001,
		Supplier1HotelId: "3001",
		GoogleGeo:        geoDomain.NewWGS84GeoPoint(39.90923, 12.397428), // 修复坐标顺序
		StaticProfile:    "{}",
		BrandId:          2,
		CreateTime:       time.Now(),
		UpdateTime:       time.Now(),
	}

	t.Run("CreateHotel", func(t *testing.T) {
		err := hotelModel.Insert(context.Background(), testHotel)
		assert.NoError(t, err)

		// 使用相同的坐标进行查询
		latlng := geoDomain.Latlng{Lng: 39.90923, Lat: 12.397428}
		cord := geoDomain.LatlngCoordinator{
			Google: &latlng,
		}
		hotels, err := hotelModel.FindNearby(context.Background(), cord, 1000)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(hotels), 1)

		found, err := hotelModel.FindByID(context.Background(), testHotel.Id)
		assert.NoError(t, err)
		assert.Equal(t, testHotel.CityRegionId, found.CityRegionId)

		found, err = hotelModel.FindBySupplierID(context.Background(), 1, "3001")
		assert.NoError(t, err)

		// 测试附近查询
		hotels, err = hotelModel.FindNearby(context.Background(),
			geoDomain.LatlngCoordinator{Google: &geoDomain.Latlng{Lng: 39.90923, Lat: 12.397428}}, 1000)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(hotels), 1)
	})
}

func TestTransaction(t *testing.T) {
	// ... 初始化代码同上 ...
	hotelModel := initHotelModel()
	t.Run("UpdateWithTx", func(t *testing.T) {
		// 先插入一个测试记录
		testHotel := &mysql.Hotel{
			Id:            time.Now().UnixNano(),
			CityRegionId:  2001,
			GoogleGeo:     geoDomain.NewWGS84GeoPoint(39.90923, 116.397428),
			StaticProfile: "{}",
			BrandId:       1,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		}
		err := hotelModel.Insert(context.Background(), testHotel)
		assert.NoError(t, err)

		err = hotelModel.TransactUpdate(context.Background(), func(s sqlx.Session) error {
			// 事务内操作
			_, err := s.Exec("UPDATE hotel SET rating = ? WHERE id = ?", 4.5, testHotel.Id)
			if err != nil {
				return err
			}

			// 验证事务可见性
			var rating float32
			err = s.QueryRow(&rating, "SELECT rating FROM hotel WHERE id = ?", testHotel.Id)
			assert.Equal(t, float32(4.5), rating)
			return nil
		})
		assert.NoError(t, err)
	})
}

func TestFindNearbyPerformance(t *testing.T) {
	hotelModel := initHotelModel()

	// 准备测试数据 - 在指定区域插入多个酒店
	testHotels := []*mysql.Hotel{
		{
			Id:            time.Now().UnixNano(), // 使用纳秒级时间戳避免冲突
			CityRegionId:  2001,
			GoogleGeo:     geoDomain.NewWGS84GeoPoint(13.045903, 47.845745), // 目标点
			StaticProfile: "{}",
			BrandId:       1,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		},
		{
			Id:            time.Now().UnixNano() + 1,
			CityRegionId:  2001,
			GoogleGeo:     geoDomain.NewWGS84GeoPoint(13.046903, 47.846745), // 附近点
			StaticProfile: "{}",
			BrandId:       2,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		},
		{
			Id:            time.Now().UnixNano() + 2,
			CityRegionId:  2001,
			GoogleGeo:     geoDomain.NewWGS84GeoPoint(13.047903, 47.847745), // 稍远点
			StaticProfile: "{}",
			BrandId:       3,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		},
	}

	// 插入测试数据
	for _, hotel := range testHotels {
		err := hotelModel.Insert(context.Background(), hotel)
		assert.NoError(t, err)
	}

	t.Run("Performance_Test", func(t *testing.T) {
		// 测试查询性能
		latlng := geoDomain.Latlng{Lng: 13.045903, Lat: 47.845745}
		cord := geoDomain.LatlngCoordinator{Google: &latlng}

		start := time.Now()
		hotels, err := hotelModel.FindNearby(context.Background(), cord, 1000)
		duration := time.Since(start)

		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(hotels), 1)

		// 性能断言：查询应该在合理时间内完成
		assert.Less(t, duration, 100*time.Millisecond, "FindNearby query took too long: %v", duration)

		t.Logf("FindNearby performance: %v, found %d hotels", duration, len(hotels))

		// 验证距离排序
		if len(hotels) > 1 {
			// 这里可以添加距离排序验证逻辑
			t.Logf("First hotel distance: %v", hotels[0].Id)
		}
	})
}
