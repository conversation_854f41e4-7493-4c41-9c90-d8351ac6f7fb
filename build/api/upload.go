package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"

	"hotel/common/log"
)

// 配置信息结构体
type Config struct {
	AccessKey   string // 七牛云AccessKey
	SecretKey   string // 七牛云SecretKey
	Bucket      string // 存储空间名称
	Domain      string // 空间域名
	LocalPath   string // 本地文件路径
	RemotePath  string // 七牛云存储路径
	Concurrency int    // 并发上传数量
	UseHTTPS    bool   // 是否使用HTTPS
}

// 从环境变量或命令行参数获取配置
func getConfig(localPath, remotePath string) Config {
	// 从命令行参数获取配置
	accessKey := flag.String("ak", os.Getenv("QINIU_ACCESS_KEY"), "七牛云AccessKey，可通过环境变量QINIU_ACCESS_KEY设置")
	secretKey := flag.String("sk", os.Getenv("QINIU_SECRET_KEY"), "七牛云SecretKey，可通过环境变量QINIU_SECRET_KEY设置")
	bucket := flag.String("bucket", os.Getenv("QINIU_BUCKET"), "存储空间名称，可通过环境变量QINIU_BUCKET设置")
	domain := flag.String("domain", os.Getenv("QINIU_DOMAIN"), "空间域名，可通过环境变量QINIU_DOMAIN设置")
	//localPath := flag.String("local", "", "本地文件或目录路径，必填")
	//remotePath := flag.String("remote", "", "七牛云存储路径，默认为文件名")
	concurrency := flag.Int("concurrency", 5, "并发上传数量")
	useHTTPS := flag.Bool("https", false, "是否使用HTTPS")

	flag.Parse()

	// SECURITY FIX: Removed hardcoded credentials
	// Use environment variables or command line parameters instead
	// Example: export QINIU_ACCESS_KEY="your_access_key"
	//          export QINIU_SECRET_KEY="your_secret_key"
	//          export QINIU_BUCKET="your_bucket"
	//          export QINIU_DOMAIN="your_domain"
	
	// Set default values only if not provided via environment or command line
	if *bucket == "" {
		*bucket = "hotelbyte" // Default bucket name
	}
	if *domain == "" {
		*domain = "sxc0vva6s.hd-bkt.clouddn.com" // Default domain
	}
	
	// 检查必填参数
	if *accessKey == "" || *secretKey == "" || *bucket == "" {
		fmt.Println("错误：AccessKey、SecretKey、Bucket和LocalPath为必填参数")
		flag.Usage()
		os.Exit(1)
	}

	// 处理默认值
	if remotePath == "" {
		remotePath = filepath.Base(localPath)
		if info, err := os.Stat(localPath); err == nil && info.IsDir() {
			remotePath = filepath.Base(localPath) + "/"
		}
	}

	return Config{
		AccessKey:   *accessKey,
		SecretKey:   *secretKey,
		Bucket:      *bucket,
		Domain:      *domain,
		LocalPath:   localPath,
		RemotePath:  remotePath,
		Concurrency: *concurrency,
		UseHTTPS:    *useHTTPS,
	}
}

// 生成上传凭证
func getUploadToken(accessKey, secretKey, bucket string) string {
	mac := qbox.NewMac(accessKey, secretKey)
	putPolicy := storage.PutPolicy{
		Scope: bucket,
	}
	return putPolicy.UploadToken(mac)
}

// 删除远程文件
func deleteFile(ctx context.Context, config Config, remotePath string) error {
	mac := qbox.NewMac(config.AccessKey, config.SecretKey)
	bucketManager := storage.NewBucketManager(mac, &storage.Config{
		UseHTTPS: config.UseHTTPS,
	})

	err := bucketManager.Delete(config.Bucket, remotePath)
	if err != nil {
		// 如果文件不存在，忽略错误
		if strings.Contains(err.Error(), "no such file") || strings.Contains(err.Error(), "not found") {
			return nil
		}
		return fmt.Errorf("删除远程文件失败: %w", err)
	}

	fmt.Printf("删除已存在的远程文件: %s\n", remotePath)
	return nil
}

// 上传单个文件
func uploadFile(ctx context.Context, config Config, localPath, remotePath string) error {
	// 先尝试删除已存在的文件
	if err := deleteFile(ctx, config, remotePath); err != nil {
		log.Info("删除远程文件失败，继续上传: %v", err)
	}

	// 生成上传凭证
	token := getUploadToken(config.AccessKey, config.SecretKey, config.Bucket)

	// 构建上传配置
	//var upHost = storage.DefaultAPIHost
	uploader := storage.NewFormUploader(&storage.Config{
		UseCdnDomains: false,
		Zone:          &storage.ZoneHuadong, // 华东区域，可根据实际情况修改
		UseHTTPS:      config.UseHTTPS,
		//UpHost:        upHost,
	})

	// 打开本地文件
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 上传文件
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{
		Params: map[string]string{
			"x:name": "value", // 自定义参数
		},
	}
	log.Info("before Put token:%s remotePath:%s,file:%s", token, remotePath, file.Name())
	err = uploader.Put(ctx, &ret, token, remotePath, file, fileInfo.Size(), &putExtra)
	if err != nil {
		return fmt.Errorf("上传文件失败: %w", err)
	}

	// 构建访问URL
	var scheme string
	if config.UseHTTPS {
		scheme = "https://"
	} else {
		scheme = "http://"
	}
	accessURL := scheme + config.Domain + "/" + ret.Key
	fmt.Printf("文件 %s 上传成功，存储路径: %s，访问URL: %s\n",
		localPath, ret.Key, accessURL)

	return nil
}

// 处理目录上传
func uploadDirectory(ctx context.Context, config Config, localDir, remoteDir string) error {
	// 遍历目录
	err := filepath.Walk(localDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return fmt.Errorf("遍历目录失败: %w", err)
		}

		// 跳过目录本身
		if path == localDir {
			return nil
		}

		// 如果是目录，创建对应的远程目录（七牛云通过空文件模拟目录）
		if info.IsDir() {
			remotePath := filepath.Join(remoteDir, filepath.Base(path), "")

			// 先尝试删除已存在的目录文件
			if err := deleteFile(ctx, config, remotePath); err != nil {
				log.Info("删除远程目录文件失败，继续创建: %v", err)
			}

			emptyFile := strings.NewReader("")

			token := getUploadToken(config.AccessKey, config.SecretKey, config.Bucket)
			uploader := storage.NewFormUploader(&storage.Config{
				UseCdnDomains: false,
				Zone:          &storage.ZoneHuadong,
				//Host:          storage.Host,
			})

			ret := storage.PutRet{}
			putExtra := storage.PutExtra{}
			err = uploader.Put(ctx, &ret, token, remotePath, emptyFile, 0, &putExtra)
			if err != nil {
				return fmt.Errorf("创建远程目录 %s 失败: %w", remotePath, err)
			}

			fmt.Printf("创建目录: %s\n", remotePath)
			return nil
		}

		// 计算远程路径
		relPath, err := filepath.Rel(localDir, path)
		if err != nil {
			return fmt.Errorf("计算相对路径失败: %w", err)
		}
		remotePath := filepath.Join(remoteDir, relPath)

		// 上传文件
		return uploadFile(ctx, config, path, remotePath)
	})

	return err
}

func upload(localPath, remotePath string) error {
	// 获取配置
	config := getConfig(localPath, remotePath)

	// 创建上下文
	ctx := context.Background()

	fmt.Printf("开始上传到七牛云，存储空间: %s，本地路径: %s\n", config.Bucket, config.LocalPath)

	// 检查本地路径是文件还是目录
	info, err := os.Stat(config.LocalPath)
	if err != nil {
		fmt.Printf("错误：无法访问本地路径 %s: %v\n", config.LocalPath, err)
		os.Exit(1)
	}

	// 执行上传
	var uploadErr error
	if info.IsDir() {
		fmt.Println("检测到本地路径为目录，开始递归上传...")
		uploadErr = uploadDirectory(ctx, config, config.LocalPath, config.RemotePath)
	} else {
		uploadErr = uploadFile(ctx, config, config.LocalPath, config.RemotePath)
	}

	if uploadErr != nil {
		fmt.Printf("上传失败: %v\n", uploadErr)
		os.Exit(1)
	}

	fmt.Println("上传完成")
	return nil
}
