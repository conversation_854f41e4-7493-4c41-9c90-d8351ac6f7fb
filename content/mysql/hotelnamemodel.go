package mysql

import (
	"context" // 新增 context 导入
	"fmt"     // 新增 fmt 导入
	"regexp"
	"strings"
	"unicode"

	"github.com/hashicorp/go-multierror"
	jsqlx "github.com/jmoiron/sqlx"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/i18n"
	"hotel/common/log"
)

// CleanString 清理字符串中的特殊字符
func CleanString(s string) string {
	if s == "" {
		return s
	}

	// 移除不可见字符和控制字符
	var result strings.Builder
	for _, r := range s {
		// 特殊处理某些字符
		switch r {
		case '\xa0': // 非断行空格
			result.WriteRune(' ')
		case '\x92': // 特殊引号字符
			result.WriteRune('\'')
		case '\x93', '\x94': // 左右双引号
			result.WriteRune('"')
		case '\x96', '\x97': // 破折号
			result.WriteRune('-')
		default:
			// 保留可打印字符、空格、标点符号等
			if unicode.IsPrint(r) || unicode.IsSpace(r) || unicode.IsPunct(r) {
				result.WriteRune(r)
			} else {
				// 将不可见字符替换为空格
				result.WriteRune(' ')
			}
		}
	}

	// 清理多余的空格
	cleaned := strings.TrimSpace(result.String())
	// 将多个连续空格替换为单个空格
	spaceRegex := regexp.MustCompile(`\s+`)
	cleaned = spaceRegex.ReplaceAllString(cleaned, " ")

	return cleaned
}

type (
	HotelNameModel struct {
		*defaultHotelNameModel
	}
)

// NewHotelNameModel returns a model for the database table.
func NewHotelNameModel(conn sqlx.SqlConn) *HotelNameModel {
	return &HotelNameModel{
		defaultHotelNameModel: newHotelNameModel(conn),
	}
}

func (m *HotelNameModel) withSession(session sqlx.Session) *HotelNameModel {
	return NewHotelNameModel(sqlx.NewSqlConnFromSession(session))
}

func (m *HotelNameModel) UpsertName(ctx context.Context, id int64, name i18n.I18N) error {
	var err *multierror.Error
	name.Range(func(lang string, value string) {
		// 清理字符串中的特殊字符
		cleanedValue := CleanString(value)
		if cleanedValue == "" {
			// 如果清理后为空，跳过这个语言
			return
		}

		// 改为UPSERT语法，参数顺序调整为：hotel_id, language, name
		_, e := m.conn.ExecCtx(ctx,
			"INSERT INTO hotel_name (hotel_id, language, name) VALUES (?, ?, ?) "+
				"ON DUPLICATE KEY UPDATE name = VALUES(name)",
			id, lang, cleanedValue)
		if e != nil {
			err = multierror.Append(err, fmt.Errorf("failed to upsert hotel name for language %s: %w", lang, e))
		}
	})
	return err.ErrorOrNil()
}

// SearchName 智能搜索方法，根据搜索词特征选择最优搜索策略
func (m *HotelNameModel) SearchName(ctx context.Context, name i18n.I18N, limit int) ([]*HotelName, error) {
	var names []*HotelName

	// 优先使用指定语言搜索，如果为空则搜索所有语言
	if name.En != "" {
		searchText := CleanString(name.En)

		// 智能选择搜索策略
		if shouldUseFulltext(searchText) {
			// 使用 FULLTEXT 索引进行高性能搜索
			query := fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND MATCH(`name`) AGAINST(? IN BOOLEAN MODE) LIMIT ?", hotelNameRows, m.table)
			err := m.conn.QueryRowsCtx(ctx, &names, query, i18n.EnUs_ISO3166, searchText, limit)
			if err != nil {
				log.Warnc(ctx, "FULLTEXT search failed, falling back to LIKE search: %v", err)
				// 降级到 LIKE 搜索
				return m.searchWithLike(ctx, i18n.EnUs_ISO3166, searchText, limit)
			}
			return names, nil
		} else {
			// 直接使用 LIKE 搜索
			return m.searchWithLike(ctx, i18n.EnUs_ISO3166, searchText, limit)
		}
	}

	if name.Zh != "" { // 如果语言为空，但文本不为空，则搜索所有语言
		searchText := CleanString(name.Zh)

		// 智能选择搜索策略
		if shouldUseFulltext(searchText) {
			// 使用 FULLTEXT 索引进行高性能搜索
			query := fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND MATCH(`name`) AGAINST(? IN BOOLEAN MODE) LIMIT ?", hotelNameRows, m.table)
			err := m.conn.QueryRowsCtx(ctx, &names, query, i18n.ZhCn_ISO3166, searchText, limit)
			if err != nil {
				log.Warnc(ctx, "FULLTEXT search failed, falling back to LIKE search: %v", err)
				// 降级到 LIKE 搜索
				return m.searchWithLike(ctx, i18n.ZhCn_ISO3166, searchText, limit)
			}
			return names, nil
		} else {
			// 直接使用 LIKE 搜索
			return m.searchWithLike(ctx, i18n.ZhCn_ISO3166, searchText, limit)
		}
	}

	// 如果 name 为空，可以返回空结果或错误，这里返回空结果
	return nil, nil
}

// searchWithLike 使用 LIKE 进行搜索
func (m *HotelNameModel) searchWithLike(ctx context.Context, language, searchText string, limit int) ([]*HotelName, error) {
	var names []*HotelName
	query := fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND `name` LIKE ? LIMIT ?", hotelNameRows, m.table)
	searchPattern := "%" + searchText + "%"
	err := m.conn.QueryRowsCtx(ctx, &names, query, language, searchPattern, limit)
	return names, err
}

// shouldUseFulltext 判断是否应该使用 FULLTEXT 搜索
func shouldUseFulltext(searchText string) bool {
	// 如果搜索词太短（小于4个字符），不使用 FULLTEXT
	if len(searchText) < 4 {
		return false
	}

	// 如果搜索词包含特殊字符或数字，使用 LIKE 搜索
	if containsSpecialChars(searchText) {
		return false
	}

	// 如果搜索词是常见词（如 "Hotel", "Inn" 等），使用 LIKE 搜索
	if isCommonWord(searchText) {
		return false
	}

	// 基于实际测试结果，对于大数据集，LIKE 搜索通常比 FULLTEXT 更快
	// 因此，我们只在特定情况下使用 FULLTEXT 搜索
	// 例如：非常具体的品牌名称或独特的地名

	// 检查是否是独特的品牌名称
	if isUniqueBrand(searchText) {
		return true
	}

	// 检查是否是独特的地名
	if isUniqueLocation(searchText) {
		return true
	}

	// 默认使用 LIKE 搜索，因为它在这个数据集上性能更好
	return false
}

// containsSpecialChars 检查是否包含特殊字符
func containsSpecialChars(text string) bool {
	for _, r := range text {
		if !unicode.IsLetter(r) && !unicode.IsSpace(r) {
			return true
		}
	}
	return false
}

// isCommonWord 检查是否是常见词
func isCommonWord(text string) bool {
	commonWords := map[string]bool{
		"hotel": true, "inn": true, "resort": true, "lodge": true,
		"motel": true, "hostel": true, "guesthouse": true, "apartment": true,
		"suite": true, "villa": true, "palace": true, "tower": true,
		"plaza": true, "center": true, "complex": true, "building": true,
	}
	return commonWords[strings.ToLower(text)]
}

// isUniqueBrand 检查是否是独特的品牌名称
func isUniqueBrand(text string) bool {
	uniqueBrands := map[string]bool{
		"hilton": true, "marriott": true, "hyatt": true, "sheraton": true,
		"intercontinental": true, "radisson": true, "courtyard": true,
		"holiday inn": true, "best western": true, "comfort inn": true,
		"quality inn": true, "ramada": true, "super 8": true,
	}
	return uniqueBrands[strings.ToLower(text)]
}

// isUniqueLocation 检查是否是独特的地名
func isUniqueLocation(text string) bool {
	// 这里可以添加一些独特的地名，但基于当前测试结果
	// 我们暂时返回 false，因为 LIKE 搜索性能更好
	return false
}

// SearchNameWithFallback 带降级策略的搜索方法
func (m *HotelNameModel) SearchNameWithFallback(ctx context.Context, name i18n.I18N, limit int) ([]*HotelName, error) {
	var names []*HotelName

	if name.En != "" {
		// 首先尝试 FULLTEXT 搜索
		query := fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND MATCH(`name`) AGAINST(? IN BOOLEAN MODE) LIMIT ?", hotelNameRows, m.table)
		searchText := CleanString(name.En)
		err := m.conn.QueryRowsCtx(ctx, &names, query, i18n.EnUs_ISO3166, searchText, limit)
		if err != nil {
			// 如果 FULLTEXT 搜索失败，降级到 LIKE 搜索
			log.Warnc(ctx, "FULLTEXT search failed, falling back to LIKE search: %v", err)
			query = fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND `name` LIKE ? LIMIT ?", hotelNameRows, m.table)
			searchText = "%" + CleanString(name.En) + "%"
			err = m.conn.QueryRowsCtx(ctx, &names, query, i18n.EnUs_ISO3166, searchText, limit)
			if err != nil {
				return nil, err
			}
		}
		return names, nil
	}

	if name.Zh != "" {
		// 首先尝试 FULLTEXT 搜索
		query := fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND MATCH(`name`) AGAINST(? IN BOOLEAN MODE) LIMIT ?", hotelNameRows, m.table)
		searchText := CleanString(name.Zh)
		err := m.conn.QueryRowsCtx(ctx, &names, query, i18n.ZhCn_ISO3166, searchText, limit)
		if err != nil {
			// 如果 FULLTEXT 搜索失败，降级到 LIKE 搜索
			log.Warnc(ctx, "FULLTEXT search failed, falling back to LIKE search: %v", err)
			query = fmt.Sprintf("SELECT %s FROM %s WHERE `language` = ? AND `name` LIKE ? LIMIT ?", hotelNameRows, m.table)
			searchText = "%" + CleanString(name.Zh) + "%"
			err = m.conn.QueryRowsCtx(ctx, &names, query, i18n.ZhCn_ISO3166, searchText, limit)
			if err != nil {
				return nil, err
			}
		}
		return names, nil
	}

	return nil, nil
}

func (m *HotelNameModel) SearchNameWithIds(ctx context.Context, name i18n.I18N, ids []int64) (names []*HotelName, err error) {
	var (
		query string
		args  []interface{}
	)
	// 优先使用指定语言搜索，如果为空则搜索所有语言
	if name.En != "" {
		searchText := CleanString(name.En)

		// 智能选择搜索策略
		if shouldUseFulltext(searchText) {
			// 使用 FULLTEXT 索引进行高性能搜索
			baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE hotel_id in (?) AND `language` = ? AND MATCH(`name`) AGAINST(? IN BOOLEAN MODE)", hotelNameRows, m.table)
			query, args, err = jsqlx.In(baseQuery, ids, i18n.EnUs_ISO3166, searchText)
		} else {
			// 使用 LIKE 搜索
			baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE hotel_id in (?) AND `language` = ? AND `name` LIKE ?", hotelNameRows, m.table)
			searchPattern := "%" + searchText + "%"
			query, args, err = jsqlx.In(baseQuery, ids, i18n.EnUs_ISO3166, searchPattern)
		}
	} else if name.Zh != "" { // 如果语言为空，但文本不为空，则搜索所有语言
		searchText := CleanString(name.Zh)

		// 智能选择搜索策略
		if shouldUseFulltext(searchText) {
			// 使用 FULLTEXT 索引进行高性能搜索
			baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE hotel_id in (?) AND `language` = ? AND MATCH(`name`) AGAINST(? IN BOOLEAN MODE)", hotelNameRows, m.table)
			query, args, err = jsqlx.In(baseQuery, ids, i18n.ZhCn_ISO3166, searchText)
		} else {
			// 使用 LIKE 搜索
			baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE hotel_id in (?) AND `language` = ? AND `name` LIKE ?", hotelNameRows, m.table)
			searchPattern := "%" + searchText + "%"
			query, args, err = jsqlx.In(baseQuery, ids, i18n.ZhCn_ISO3166, searchPattern)
		}
	}
	if err != nil {
		return nil, err
	}
	err = m.conn.QueryRowsCtx(ctx, &names, query, args...)
	if err != nil {
		return nil, err
	}
	return names, nil
}
