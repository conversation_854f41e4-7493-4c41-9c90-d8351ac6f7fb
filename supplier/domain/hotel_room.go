package domain

import (
	"hotel/common/i18n"
	"hotel/common/money"
	"hotel/common/types"
)

type WindowType int

const (
	Window_Type_NoWindow WindowType = iota
	Window_Type_HasWindow
	Window_Type_Unknown
)

type HotelRoomStaticProfile struct {
	RoomTypeID      string           `json:"roomTypeId,omitempty" required:"true"` // roomTypeID
	HotelId         int64            `json:"hotelId,omitempty" required:"true"`    // hotelID
	SupplierHotelId string           `json:"supplierHotelId,omitempty"`
	RoomName        i18n.I18N        `json:"roomName,omitzero" required:"true"`
	Pictures        HotelPictureList `json:"pictures,omitzero"`
	// 下面的字段还需要商榷
	RoomDesc   i18n.I18N      `json:"roomDesc,omitzero"`
	WindowType WindowType     `json:"windowType,omitempty" default:"0"` // 0: no window, 1: has window, 2: no preference
	BedType    int64          `json:"bedType,omitempty"`                // 0: no preference, 1: single bed, 2: double bed, 3: twin beds
	BedCount   int64          `json:"bedCount,omitempty"`               // number of beds in the room, 0 means no preference
	BedDesc    i18n.I18N      `json:"bedDesc,omitempty"`                // bed description, e.g. "1 King Bed", "2 Twin Beds"
	Occupancy  int64          `json:"occupancy,omitempty"`              // 可入住人数
	RoomArea   string         `json:"roomArea,omitempty"`               // room area in square meters
	Images     []string       `json:"images,omitempty"`                 // room images, URLs;  deprecated, use pictures instead
	Smoking    bool           `json:"smoking,omitempty"`                // smoking room or not
	Profile    map[string]any `json:"profile,omitempty"`                // other static profile information, e.g. "hasWifi": true, "hasWindow": true
}

type HotelRoomDynamicProfile struct {
	IsAvailable      bool  `json:"isAvailable,omitempty"`      // it has no available room offers if it is false
	MasterRoomTypeId int64 `json:"masterRoomTypeId,omitempty"` // standard room type SupplierHotelId
	MasterBedTypeId  int64 `json:"masterBedTypeId,omitempty"`  // standard room type SupplierHotelId
}

type RoomMappingProcess struct {
	MappedRoomID     string  `json:"mappedRoomId,omitempty"`     // 映射后的平台统一 RoomID
	Confidence       float64 `json:"confidence,omitempty"`       // 置信度 0~1
	OlivierMapped    bool    `json:"olivierMapped,omitempty"`    // 是否经过 Olivier 映射
	GiataMapped      bool    `json:"giataMapped,omitempty"`      // 是否经过 GIATA 映射
	GiataConfidence  float64 `json:"giataConfidence,omitempty"`  // GIATA 映射置信度
}

type Room struct {
	HotelRoomStaticProfile
	HotelRoomDynamicProfile
	Rates          []RoomRatePkg       `json:"rates,omitzero"`
	MappingProcess *RoomMappingProcess `json:"mappingProcess,omitempty"`
}
type RoomList []Room
type RoomRatePkg struct {
	RatePkgId string `json:"ratePkgId,omitempty" required:"true"` // used as key input for checkAvail & book APIs
	// RatePlan 是一个动态的规则，可以横跨多个 ratePkg；这里是适配了当前 ratePkg 做了字段值修改的的 rateplan
	RatePlan
	SupplierRatePkgKey SupplierRatePkgKey       `json:"supplierRatePkgKey,omitzero"`
	Rate               Rate                     `json:"rate,omitzero"`
	Available          bool                     `json:"available,omitempty"`
	Timestamp          int64                    `json:"timestamp,omitempty"`
	Trace              InternalRoomRatePkgTrace `json:"trace,omitzero"`
	CheckIn            types.DateInt            `json:"checkIn,omitempty"`
	CheckOut           types.DateInt            `json:"checkOut,omitempty"`
}

type SupplierRatePkgKey struct {
	Id   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type InternalRoomRatePkgTrace struct {
	OriginRate Rate   `json:"originRate,omitzero"`
	RateRule   string `json:"rateRule,omitzero"`
}

type BedOption struct {
	ID   int64  `json:"id"`
	Desc string `json:"desc"` // "1 Queen BedTypes"
}
type PaymentOption struct {
}
type Amenity struct {
}

type Rate struct {
	FinalRate        money.Money `json:"finalRate,omitzero" apidoc:"HotelCode"` // for front-end, FinalRate is required and other fields are hidden
	NetRate          money.Money `json:"netRate,omitzero"`
	GrossRate        money.Money `json:"grossRate,omitzero"`
	RespectGrossRate bool        `json:"respectGrossRate,omitzero"` // You should respect GrossRate if RespectGrossRate is true; default as false
}
