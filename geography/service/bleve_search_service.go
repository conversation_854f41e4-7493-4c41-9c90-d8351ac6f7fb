package service

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"time"

	"github.com/blevesearch/bleve/v2/analysis/token/ngram"

	"github.com/hashicorp/go-set/v3"

	"github.com/blevesearch/bleve/v2/analysis/analyzer/custom"
	"github.com/blevesearch/bleve/v2/analysis/analyzer/keyword"
	"github.com/blevesearch/bleve/v2/analysis/token/edgengram"
	"github.com/blevesearch/bleve/v2/analysis/token/lowercase"
	"github.com/blevesearch/bleve/v2/analysis/tokenizer/unicode"

	"github.com/blevesearch/bleve/v2/mapping"
	"github.com/blevesearch/bleve/v2/search"

	"github.com/blevesearch/bleve/v2/search/query"
	pkgerr "github.com/pkg/errors"

	"hotel/common/bizerr"
	"hotel/common/envhelper"
	"hotel/common/log"
	"hotel/common/types"

	configutils "hotel/common/config"
	"hotel/common/i18n"
	searchUtils "hotel/common/search"
	"hotel/common/utils"
	"hotel/geography/domain"

	"github.com/blevesearch/bleve/v2"
	_ "github.com/blevesearch/bleve/v2/config"
	_ "github.com/blevesearch/bleve/v2/index/scorch"
	"github.com/bytedance/sonic"
	"github.com/spf13/cast"
)

type BleveSearchService struct {
	index        bleve.Index
	indexManager *InMemoryIndexManager
}

const (
	// type DocumentMapping struct {
	//	Enabled         bool                        `json:"enabled"`
	//	Dynamic         bool                        `json:"dynamic"`
	//	Properties      map[string]*DocumentMapping `json:"properties,omitempty"`
	//	Fields          []*FieldMapping             `json:"fields,omitempty"`
	//	DefaultAnalyzer string                      `json:"default_analyzer,omitempty"`
	//
	//	// StructTagKey overrides "json" when looking for field names in struct tags
	//	StructTagKey string `json:"struct_tag_key,omitempty"`
	//}
	defaultMappingConfig = ``
)

func NewBleveSearchService(indexManager *InMemoryIndexManager) (*BleveSearchService, error) {
	//indexPath := path.Join(configutils.GetProjectPath(), "geography/bleve_index_"+cast.ToString(time.Now().Unix()))
	st := time.Now()
	defer func() {
		log.Info("NewBleveSearchService cost:%s ", time.Since(st))
	}()
	indexPath := path.Join(configutils.GetProjectPath(), "geography/bleve_index")
	index, err := initBleve(indexPath, defaultMappingConfig, indexManager.GetAllRegionMap())
	if err != nil {
		return nil, fmt.Errorf("initBleve failed:%v by indexPath:%s", err, indexPath)
	}

	out := &BleveSearchService{indexManager: indexManager, index: index}
	if hs := out.HealthyStatus(); hs != "" {
		return nil, errors.New(hs)
	}
	return out, nil
}

// Search
// Query Type	    精准度	    灵活性	    性能	        典型场景
// Term	            ★★★★★	★☆☆☆☆	★★★★★	精确值匹配
// Phrase	        ★★★★★	★☆☆☆☆	★★★★☆	严格顺序短语
// Match	        ★★☆☆☆	★★★★★	★★★☆☆	通用全文搜索
// Match Phrase	    ★★★★☆	★★★★☆	★★★☆☆	兼顾分词和顺序的短语
// PrefixNgram	        ★★★☆☆	★★★☆☆	★★★☆☆	前缀补全
// Regexp/Wildcard	★☆☆☆☆	★★★★★	☆☆☆☆☆	特殊模式匹配（慎用）
// Fuzzy	        ★★☆☆☆	★★★★☆	★★☆☆☆	拼写容错
// Range	        ★★★★☆	★★☆☆☆	★★★★☆	数值/日期范围
// Compound	        可变	        可变	        可变	        复杂逻辑组合
func (s *BleveSearchService) Search(ctx context.Context, keyword string, page, size int) ([]*domain.Region, *bleve.SearchResult, error) {
	return s.search(ctx, s.generateQuery(keyword), page, size)
}

func (s *BleveSearchService) Query(ctx context.Context, q string, page, size int) ([]*domain.Region, *bleve.SearchResult, error) {
	return s.search(ctx, query.NewQueryStringQuery(q), page, size)
}
func (s *BleveSearchService) generateQuery(term string) query.Query {
	q := bleve.NewBooleanQuery()
	//"name.keyword:Beijing", "name.ngram:Beijing", "name.prefix_ngram:Beij", "name.suffix_ngram:ijing"
	q.AddShould(bleve.NewQueryStringQuery("name.keyword:" + term))
	q.AddShould(bleve.NewQueryStringQuery("name.ngram:" + term))
	q.AddShould(bleve.NewQueryStringQuery("name.prefix_ngram:" + term))
	q.AddShould(bleve.NewQueryStringQuery("name.suffix_ngram:" + term))

	// 6. 中文场景增强（覆盖中文前后缀及子串）
	if i18n.ContainsChinese(term) {
		q.AddShould(bleve.NewQueryStringQuery("nameZh.keyword:" + term))
		q.AddShould(bleve.NewQueryStringQuery("nameZh.jieba:" + term))
	}

	q.SetMinShould(1) // 至少匹配1个should子句（Bleve实际API）
	return q
}

// 辅助函数：逆序字符串（如"Be"→"eB"）
func reverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func (s *BleveSearchService) search(ctx context.Context, q query.Query, page, size int) ([]*domain.Region, *bleve.SearchResult, error) {
	if s.index == nil {
		return nil, nil, bizerr.SystemErr.WithMessage("index not initialized")
	}
	searchReq := bleve.NewSearchRequest(q)
	searchReq.From = (page - 1) * size
	searchReq.Size = size
	searchReq.Sort = []search.SearchSort{&search.SortScore{Desc: true}}
	searchReq.Explain = true
	searchReq.ClientContextID = log.GetOrNewLogidFromContext(ctx)

	searchRes, err := s.index.SearchInContext(ctx, searchReq)
	if err != nil {
		return nil, nil, err
	}

	// 转换为 Region 对象
	idset := set.New[string](len(searchRes.Hits))
	var results []*domain.Region
	for _, hit := range searchRes.Hits {
		if idset.Contains(hit.ID) {
			panic(fmt.Errorf("duplicated id recalled:%v", hit.ID))
		}
		idset.Insert(hit.ID)
		if region, ok := s.indexManager.GetRegionByID(types.ID(cast.ToInt64(hit.ID))); ok { // 依赖全局 indexManager
			results = append(results, region)
		} else {
			log.Infoc(ctx, "hit but not found by %v", hit.ID)
		}
	}
	return results, searchRes, nil
}

func (s *BleveSearchService) HealthyStatus() string {
	for _, q := range []string{"name:Beijing" /*不行*/, "name.keyword:Beijing", "name.ngram:Beijing", "name.prefix_ngram:Beij", "name.suffix_ngram:ijing"} {
		v, err := s.searchQueryString(q)
		if err != nil {
			return err.Error()
		}
		if len(v) == 0 {
			log.Info("can't execute basic query of Beijing by %s", q)
			continue
		}
		log.Info("query result of query of Beijing by %s, got(%s)", q, utils.ToJSON(v))
	}
	return ""
}

func (s *BleveSearchService) searchQueryString(q string) ([]*domain.Region, error) {
	v, _, err := s.search(context.Background(), bleve.NewQueryStringQuery(q), 1, 1)
	return v, err
}

func (s *BleveSearchService) Close() error {
	return utils.CloseE(s.index)
}

// 初始化 Bleve 索引
func initBleve(indexPath string, mappingConfig string, data map[types.ID]*domain.Region) (bleve.Index, error) {
	im := bleve.NewIndexMapping()
	if mappingConfig != "" {
		if err := sonic.UnmarshalString(mappingConfig, &im); err != nil {
			return nil, fmt.Errorf("parse IndexMapping failed:%v from: %s", err, mappingConfig)
		}
	} else {
		analyzer(im)
		m := bleve.NewDocumentMapping()
		m.Dynamic = false
		// Region结构体的name字段，要映射成一个多field的结构，用来做不同的匹配（避免ngram干扰）
		k := "name"
		nameDoc := bleve.NewDocumentMapping()
		nameDoc.Dynamic = false
		nameDoc.AddFieldMappingsAt(nameNgram, &mapping.FieldMapping{
			Name:               nameNgram,
			Type:               "text",
			Analyzer:           nameNgram,
			Store:              true,
			Index:              true,
			IncludeTermVectors: true,  // default true
			IncludeInAll:       false, // default true
			DocValues:          false,
		})
		nameDoc.AddFieldMappingsAt(namePrefixNgram, &mapping.FieldMapping{
			Name:               namePrefixNgram,
			Type:               "text",
			Analyzer:           namePrefixNgram,
			Store:              true,
			Index:              true,
			IncludeTermVectors: true,  // default true
			IncludeInAll:       false, // default true
			DocValues:          false,
		})
		nameDoc.AddFieldMappingsAt(nameSuffixNgram, &mapping.FieldMapping{
			Name:               nameSuffixNgram,
			Type:               "text",
			Analyzer:           nameSuffixNgram,
			Store:              true,
			Index:              true,
			IncludeTermVectors: true,  // default true
			IncludeInAll:       false, // default true
			DocValues:          false,
		})
		nameDoc.AddFieldMappingsAt(keyword.Name, &mapping.FieldMapping{
			Name:               keyword.Name,
			Type:               "text",
			Analyzer:           searchUtils.NameCaseInsensitiveKeyword,
			Store:              true,
			Index:              true,
			IncludeTermVectors: true,  // default true
			IncludeInAll:       false, // default true
			DocValues:          true,
		})
		m.AddSubDocumentMapping(k, nameDoc)
		nameZhDoc := bleve.NewDocumentMapping()
		nameZhDoc.Dynamic = false
		nameZhDoc.AddFieldMappingsAt("jieba", &mapping.FieldMapping{
			Type:               "text",
			Analyzer:           searchUtils.NameJiaba,
			Store:              true,
			Index:              true,
			IncludeTermVectors: false, // default true
			IncludeInAll:       false, // default true
			DocValues:          false,
		})
		nameZhDoc.AddFieldMappingsAt("keyword", &mapping.FieldMapping{
			Type:               "text",
			Analyzer:           keyword.Name,
			Store:              true,
			Index:              true,
			IncludeTermVectors: false,
			IncludeInAll:       false,
			DocValues:          false,
		})

		// 3. 将extra映射关联到主文档
		m.AddSubDocumentMapping("nameZh", nameZhDoc) // 关键步骤

		im.DefaultMapping = m
		//im.DefaultField = "name" // _all
		//im.StoreDynamic = falseto
		//im.IndexDynamic = false
		//im.DocValuesDynamic = false
	}
	// log.Info("NewBleveSearchService index im: %s", utils.ToJSON(im))
	// 1. 预处理路径（转换为绝对路径并清理符号链接）
	absPath, err := filepath.Abs(indexPath)
	if err != nil {
		return nil, fmt.Errorf("解析路径失败: %w", err)
	}

	// 2. 检查/创建目录（确保权限正确）
	if err := os.MkdirAll(absPath, 0755); err != nil {
		return nil, fmt.Errorf("创建目录失败: %w", err)
	}

	// 3. 尝试打开现有索引
	index, err := bleve.Open(absPath)
	if err == nil {
		if _, err = buildIndex(index, data); err != nil {
			return nil, pkgerr.Wrap(err, "existed index update failed")
		}
		return index, nil
	} else if !(errors.Is(err, bleve.ErrorIndexMetaMissing) || errors.Is(err, bleve.ErrorIndexPathDoesNotExist)) {
		return nil, fmt.Errorf("bleve.Open %s failed:%v", indexPath, err)
	}

	log.Info("bleve create index file")
	// 使用 NewUsing 创建/打开索引
	index, err = bleve.New(indexPath, im)
	if err != nil {
		return nil, fmt.Errorf("bleve.New failed:%v", err)
	}
	if _, err = buildIndex(index, data); err != nil {
		return nil, pkgerr.Wrap(err, "new index failed")
	}
	return index, nil
}

func getBatchSize() int {
	if envhelper.IsUAT() {
		return 500 //cpu 弱的话就小一点咯
	}
	return 50000
}

func getWaitDuration() time.Duration {
	if envhelper.IsUAT() {
		return 500 * time.Millisecond
	}
	return time.Millisecond
}

func buildIndex(index bleve.Index, data map[types.ID]*domain.Region) (int, error) {
	originalDocCount, _ := index.DocCount()
	batchSize := getBatchSize()
	// 索引数据
	st := time.Now()
	batch := index.NewBatch()
	cnt := 0
	for _, region := range data {
		id := cast.ToString(region.ID)
		if doc, err := index.Document(id); doc != nil {
			// don't update
			continue
		} else if err != nil {
			panic(fmt.Errorf("id(%v) %w", id, err))
		}
		if err := batch.Index(id, region.ToRegionIndexDoc()); err != nil {
			return 0, err
		}
		if batch.Size() >= batchSize {
			if err := index.Batch(batch); err != nil {
				return 0, fmt.Errorf("BatchIndex failed:%v", err)
			}
			cnt += batch.Size()
			batch = index.NewBatch()
			time.Sleep(getWaitDuration()) // 60_0000 / 5000 = 1200ms
		}
	}
	if err := index.Batch(batch); err != nil {
		return 0, fmt.Errorf("BatchIndex failed:%v", err)
	}
	cnt += batch.Size()
	log.Info("NewBleveSearchService index cost: %s, updated: %d, originalDocCount: %d", time.Since(st), cnt, originalDocCount)
	return batch.Size(), nil
}

func analyzer(m *mapping.IndexMappingImpl) {
	//standard.Name
	//"tokenizer":         standard.NameCaseInsensitiveKeyword,
	//if err := m.AddCustomTokenizer(nameRegion, map[string]interface{}{
	//	"type":      nameRegion,
	//	"tokenizer": unicode.NameCaseInsensitiveKeyword,
	//	//"char_filter": "index_region_char_filter",
	//	"filter": []string{
	//		lowercase.NameCaseInsensitiveKeyword,
	//		en.StopName,
	//		//"trim",
	//	},
	//}); err != nil {
	//	panic(err)
	//}
	//if err := m.AddCustomCharFilter("index_region_char_filter", map[string]interface{}{
	//	"mappings": []string{
	//		"\\u0020 => ",
	//		"- => ",
	//	},
	//	"type": "mapping",
	//}); err != nil {
	//	panic(err)
	//}
	// "index_region_analyzer": {
	//          "char_filter": [
	//            "index_region_char_filter"
	//          ],
	//          "filter": [
	//            "lowercase",
	//            "region_filter",
	//            "trim"
	//          ],
	//          "tokenizer": "standard",
	//          "type": "custom"
	//        },
	if err := m.AddCustomAnalyzer(searchUtils.NameCaseInsensitiveKeyword, map[string]interface{}{
		"type": searchUtils.NameCaseInsensitiveKeyword,
	}); err != nil {
		panic(err)
	}
	if err := m.AddCustomAnalyzer(searchUtils.NameJiaba,
		map[string]interface{}{
			"type": searchUtils.NameJiaba,
		},
	); err != nil {
		panic(err)
	}
	//         "search_region_analyzer": {
	//          "char_filter": [
	//            "index_region_char_filter"
	//          ],
	//          "filter": [
	//            "lowercase",
	//            "trim"
	//          ],
	//          "tokenizer": "standard",
	//          "type": "custom"
	//        }

	//       "char_filter": {
	//        "index_region_char_filter": {
	//          "mappings": [
	//            "\\u0020 => ",
	//            "- => "
	//          ],
	//          "type": "mapping"
	//        }
	//      },

	// 自定义 ngram 分词器 (支持中英文子串)
	if err := m.AddCustomTokenFilter(namePrefixNgram, map[string]interface{}{
		"type": edgengram.Name,
		"min":  float64(2),
		"max":  float64(5), // es
	}); err != nil {
		panic(err)
	}
	if err := m.AddCustomTokenFilter(nameSuffixNgram, map[string]interface{}{
		"type": edgengram.Name,
		"back": true,
		"min":  float64(2),
		"max":  float64(5), // es
	}); err != nil {
		panic(err)
	}
	if err := m.AddCustomTokenFilter(nameNgram, map[string]interface{}{
		"type": ngram.Name,
		"min":  float64(2),
		"max":  float64(20), // es
	}); err != nil {
		panic(err)
	}
	// 自定义 ngram 分词器 (支持中英文子串)
	if err := m.AddCustomAnalyzer(namePrefixNgram,
		map[string]interface{}{
			"type":      custom.Name,  // 用户自定义分析器
			"tokenizer": unicode.Name, // 保留全文不分词
			"token_filters": []string{
				lowercase.Name,
				searchUtils.NameTrim,
				namePrefixNgram, // 应用刚刚定义的ngram过滤
			},
		}); err != nil {
		panic(err)
	}
	if err := m.AddCustomAnalyzer(nameSuffixNgram,
		map[string]interface{}{
			"type":      custom.Name,  // 用户自定义分析器
			"tokenizer": unicode.Name, // 保留全文不分词
			"token_filters": []string{
				lowercase.Name,
				searchUtils.NameTrim,
				nameSuffixNgram, // 应用刚刚定义的ngram过滤
			},
		}); err != nil {
		panic(err)
	}
	//// 自定义 ngram 分词器 (支持中英文子串)
	if err := m.AddCustomAnalyzer(nameNgram,
		map[string]interface{}{
			"type":      custom.Name,  // 用户自定义分析器
			"tokenizer": unicode.Name, // 保留全文不分词
			"token_filters": []string{
				lowercase.Name,
				searchUtils.NameTrim,
				nameNgram, // 应用刚刚定义的ngram过滤
			},
		}); err != nil {
		panic(err)
	}
}

const (
	namePrefixNgram = "prefix_ngram"
	nameSuffixNgram = "suffix_ngram"
	nameNgram       = "ngram"
)
