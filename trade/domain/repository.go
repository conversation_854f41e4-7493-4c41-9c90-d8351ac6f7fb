package domain

import (
	"hotel/common/types"
	"time"
)

// OrderQueryCriteria 订单查询条件
type OrderQueryCriteria struct {
	CustomerID       *types.ID    `json:"customerId,omitempty"`
	Status           *OrderStatus `json:"status,omitempty"`
	ReferenceNo      *string      `json:"referenceNo,omitempty"`
	CreateTimeWindow *TimeWindow  `json:"createTimeWindow,omitempty"`
	Page             int          `json:"page"`
	PageSize         int          `json:"pageSize"`
}

// TimeWindow 时间窗口
type TimeWindow struct {
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
}

// SupplierOrderStatus 供应商订单状态
type SupplierOrderStatus int64

const (
	SupplierOrderStatusCreated   SupplierOrderStatus = 1 // 已创建
	SupplierOrderStatusSubmitted SupplierOrderStatus = 2 // 已提交
	SupplierOrderStatusConfirmed SupplierOrderStatus = 3 // 已确认
	SupplierOrderStatusCancelled SupplierOrderStatus = 4 // 已取消
	SupplierOrderStatusFailed    SupplierOrderStatus = 5 // 失败
)

func (s SupplierOrderStatus) String() string {
	switch s {
	case SupplierOrderStatusCreated:
		return "已创建"
	case SupplierOrderStatusSubmitted:
		return "已提交"
	case SupplierOrderStatusConfirmed:
		return "已确认"
	case SupplierOrderStatusCancelled:
		return "已取消"
	case SupplierOrderStatusFailed:
		return "失败"
	default:
		return "未知状态"
	}
}

func (s SupplierOrderStatus) Int64() int64 {
	return int64(s)
}
