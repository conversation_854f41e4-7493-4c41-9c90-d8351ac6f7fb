# 系统架构设计

本文档描述了 Hotel-BE 酒店分销平台的系统架构设计，包括整体架构、技术选型、模块设计等。

## 🏗️ 架构概览

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin-FE      │    │   Landing-FE    │    │   Mobile App    │
│   (管理后台)     │    │   (落地页)       │    │   (移动端)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (API网关)      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Service  │    │  Search Service │    │  Trade Service  │
│   (用户服务)     │    │  (搜索服务)     │    │  (订单服务)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Content Service │    │ Geography Svc   │    │  Rule Engine    │
│ (内容管理服务)   │    │ (地理服务)       │    │  (规则引擎)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Supplier Layer │
                    │  (供应商层)      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Dida        │    │   DerbySoft     │    │   HotelBeds     │
│   (供应商)       │    │   (供应商)       │    │   (供应商)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈

| 层级 | 技术选型 | 说明 |
|------|----------|------|
| **前端** | Vue 3 + TypeScript | 现代化前端框架 |
| **API网关** | go-zero | 高性能API网关 |
| **微服务** | Go + go-zero | 高性能后端服务 |
| **数据库** | MySQL + Redis | 关系型数据库 + 缓存 |
| **消息队列** | Redis Stream | 轻量级消息队列 |
| **容器化** | Docker | 应用容器化 |
| **监控** | Prometheus + Grafana | 系统监控 |

## 🏛️ 核心模块设计

### 1. 用户服务 (User Service)

**职责**: 用户管理、认证授权、租户管理

**核心功能**:
- 用户注册/登录
- 权限管理
- 租户管理
- 实体管理

**技术特点**:
- JWT认证
- RBAC权限模型
- 多租户支持

### 2. 搜索服务 (Search Service)

**职责**: 酒店搜索、筛选、排序

**核心功能**:
- 酒店搜索
- 地理位置搜索
- 价格筛选
- 智能排序

**技术特点**:
- 全文搜索
- 地理位置索引
- 缓存优化

### 3. 订单服务 (Trade Service)

**职责**: 订单全生命周期管理

**核心功能**:
- 订单创建
- 支付处理
- 订单状态管理
- 重预订功能

**技术特点**:
- 状态机模式
- 异步处理
- 事务管理

### 4. 内容服务 (Content Service)

**职责**: 酒店信息管理

**核心功能**:
- 酒店信息管理
- 房型管理
- 图片管理
- 数据同步

**技术特点**:
- 数据同步
- 图片处理
- 多语言支持

### 5. 地理服务 (Geography Service)

**职责**: 地理位置相关服务

**核心功能**:
- 城市管理
- 地理位置查询
- 距离计算

**技术特点**:
- 空间索引
- 地理位置算法
- 缓存优化

### 6. 规则引擎 (Rule Engine)

**职责**: 业务规则管理

**核心功能**:
- 定价规则
- 预订规则
- 促销规则

**技术特点**:
- 规则引擎
- 动态配置
- 规则缓存

## 🔌 供应商集成

### 供应商抽象层

```go
// 供应商接口
type Supplier interface {
    Search(ctx context.Context, req *SearchRequest) (*SearchResponse, error)
    Book(ctx context.Context, req *BookRequest) (*BookResponse, error)
    Cancel(ctx context.Context, req *CancelRequest) (*CancelResponse, error)
    GetOrder(ctx context.Context, req *GetOrderRequest) (*GetOrderResponse, error)
}
```

### 支持的供应商

| 供应商 | 状态 | 功能支持 |
|--------|------|----------|
| Dida | ✅ 生产 | 搜索、预订、取消 |
| DerbySoft | ✅ 生产 | 搜索、预订、取消 |
| HotelBeds | ✅ 生产 | 搜索、预订、取消 |
| Expedia | 🔄 开发中 | 搜索、预订 |
| Ctrip | 🔄 开发中 | 搜索、预订 |

## 🗄️ 数据架构

### 数据库设计原则

1. **微服务数据库分离**
   - 每个微服务拥有独立的逻辑数据库
   - 基于运维成本考虑，多个逻辑数据库可映射到同一物理数据库

2. **数据库映射关系**

| 逻辑数据库 | 物理数据库 | 服务 |
|-----------|-----------|------|
| search | item | 搜索服务 |
| supplier | item | 供应商服务 |
| mapping | item | 映射服务 |
| geography | item | 地理服务 |
| content | item | 内容管理 |
| simulator | item | 模拟服务 |
| user | user | 用户服务 |
| rule | user | 规则引擎 |
| trade | user | 订单服务 |

### 缓存策略

1. **Redis缓存层级**
   - L1: 应用级缓存（内存）
   - L2: 分布式缓存（Redis）
   - L3: 数据库

2. **缓存策略**
   - 热点数据缓存
   - 查询结果缓存
   - 会话数据缓存

## 🔄 消息队列

### 消息队列架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Producer  │───▶│ Redis Stream│───▶│  Consumer   │
│  (生产者)    │    │ (消息队列)   │    │  (消费者)   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 主要队列

| 队列名称 | 用途 | 消费者 |
|----------|------|--------|
| order_book | 订单预订 | Trade Service |
| order_cancel | 订单取消 | Trade Service |
| order_refund | 订单退款 | Trade Service |
| hotel_sync | 酒店同步 | Content Service |
| price_update | 价格更新 | Search Service |

## 🔒 安全架构

### 认证授权

1. **JWT认证**
   - 无状态认证
   - Token刷新机制
   - 多端支持

2. **权限控制**
   - RBAC权限模型
   - 细粒度权限控制
   - 动态权限配置

### 数据安全

1. **数据加密**
   - 传输加密（HTTPS）
   - 存储加密（敏感数据）
   - 密钥管理

2. **访问控制**
   - API访问控制
   - 数据库访问控制
   - 文件访问控制

## 📊 监控告警

### 监控指标

1. **应用监控**
   - 响应时间
   - 错误率
   - 吞吐量
   - 资源使用率

2. **业务监控**
   - 订单量
   - 搜索量
   - 用户活跃度
   - 收入指标

### 告警策略

1. **技术告警**
   - 服务不可用
   - 响应时间超时
   - 错误率过高
   - 资源使用率过高

2. **业务告警**
   - 订单异常
   - 支付异常
   - 数据同步异常

## 🚀 部署架构

### 容器化部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  api-gateway:
    image: hotel-be/api-gateway:latest
    ports:
      - "8080:8080"
    environment:
      - ENV=production
      
  user-service:
    image: hotel-be/user-service:latest
    environment:
      - ENV=production
      
  search-service:
    image: hotel-be/search-service:latest
    environment:
      - ENV=production
      
  trade-service:
    image: hotel-be/trade-service:latest
    environment:
      - ENV=production
```

### 环境管理

| 环境 | 用途 | 特点 |
|------|------|------|
| Development | 开发环境 | 本地开发，热更新 |
| Testing | 测试环境 | 功能测试，集成测试 |
| Staging | 预生产环境 | 性能测试，用户验收 |
| Production | 生产环境 | 正式服务，高可用 |

## 📈 性能优化

### 性能指标

| 指标 | 目标值 | 监控方式 |
|------|--------|----------|
| API响应时间 | < 100ms | Prometheus |
| 数据库查询时间 | < 50ms | MySQL慢查询日志 |
| 缓存命中率 | > 90% | Redis监控 |
| 系统可用性 | > 99.9% | 健康检查 |

### 优化策略

1. **应用层优化**
   - 代码优化
   - 算法优化
   - 并发优化

2. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池优化

3. **缓存优化**
   - 缓存策略优化
   - 缓存预热
   - 缓存失效策略

## 🔮 未来规划

### 短期规划（3-6个月）
1. **功能完善**
   - 完善供应商集成
   - 优化搜索算法
   - 增强订单管理

2. **性能优化**
   - 数据库优化
   - 缓存优化
   - 代码优化

### 中期规划（6-12个月）
1. **架构升级**
   - 微服务拆分
   - 消息队列升级
   - 监控体系完善

2. **业务扩展**
   - 新供应商接入
   - 新功能模块
   - 国际化支持

### 长期规划（1年以上）
1. **技术升级**
   - 新技术引入
   - 架构重构
   - 性能提升

2. **业务发展**
   - 新业务线
   - 合作伙伴
   - 市场扩展

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队
