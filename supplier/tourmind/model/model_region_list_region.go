/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RegionListRegion struct for RegionListRegion
type RegionListRegion struct {
	// ISO 3166-1 alpha-2, e.g., China: CN.,
	CountryCode string `json:"CountryCode,omitempty"`
	// Region name.
	Name string `json:"Name,omitempty"`
	// Region name in Chinese. (Nullable.)
	NameCN string `json:"NameCN,omitempty"`
	// TourMind region ID.
	RegionID string `json:"RegionID,omitempty"`
	// Region name
	RegionNameLong string `json:"RegionNameLong,omitempty"`
	// Full region name in Chinese.
	RegionNameLongCN string `json:"RegionNameLongCN,omitempty"`
}
