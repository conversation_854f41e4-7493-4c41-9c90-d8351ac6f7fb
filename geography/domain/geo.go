package domain

import (
	"strings"

	"hotel/common/idgen"
	"hotel/common/types"
)

// CtripCountryBaseInfo defines the basic information for a country
type CtripCountryBaseInfo struct {
	CountryId     int64  `json:"countryId"`
	Name          string `json:"name"` // Country name (multi-language support)
	EnName        string `json:"enName"`
	Code          string `json:"code"`        // ISO 国家二字码
	ContinentId   int64  `json:"continentId"` // Continent SupplierHotelId where the country is located
	ContinentName string `json:"continentName"`
	AreaCode      string `json:"areaCode"` // 接口文档没有，暂不清楚具体含义
}

type CtripProvinceCondition struct {
	ProvinceIDs                   string                        `json:"provinceIds"`                             // 英文逗号分割
	ProvinceNames                 string                        `json:"provinceNames"`                           // 英文逗号分割；优先省份 id
	PrefectureLevelCityConditions *PrefectureLevelCityCondition `json:"prefectureLevelCityConditions,omitempty"` // Optional, filter by city conditions 地级市查询条件
}

type CtripPOICondition struct {
	ReturnAirport      *bool `json:"returnAirport"`      // 默认 true
	ReturnTrainStation *bool `json:"returnTrainStation"` // 默认 true
	ReturnBusStation   *bool `json:"returnBusStation"`   // 默认 true
}

// PrefectureLevelCityCondition defines conditions for filtering prefecture-level cities
type PrefectureLevelCityCondition struct {
	PrefectureLevelCityNames []string `json:"prefectureLevelCityNames"`
	PrefectureLevelCityIDs   []string `json:"prefectureLevelCityIds"`
	ReturnDistrict           *bool    `json:"returnDistrict"` // 是否返回地级市下属区,默认 true
	ReturnCounty             *bool    `json:"returnCounty"`   // 是否返回地级市下属县,默认 true
}

// CtripPOIData defines the basic information for a Point of Interest (POI)
// Structure needs to be defined based on actual API response or documentation
type CtripPOIData struct {
	ProvinceID                  int64                     `json:"provinceId,omitempty"`
	ProvinceName                string                    `json:"provinceName,omitempty"`
	ProvinceEnName              string                    `json:"provinceEnName,omitempty"`
	PrefectureLevelCityInfoList []PrefectureLevelCityInfo `json:"prefectureLevelCityInfoList"`
}

type PrefectureLevelCityInfo struct {
	CityID       int64                `json:"cityId"`
	CityName     string               `json:"cityName"`
	CityEnName   string               `json:"cityEnName"`
	CorpTag      int64                `json:"corpTag"`     // 0标准城市信息 1：非标准，只可预订机票
	StationInfo  StationInfo          `json:"stationInfo"` // 交通站
	CountyList   []CountyLevelCityPOI `json:"countyList"`
	DistrictList []DistrictPOIInfo    `json:"districtList"`
	DistrictCode string               `json:"districtCode"` // 行政区划代码
	CityCode     string               `json:"cityCode"`
	CityPinyin   string               `json:"cityPinyin"`
}

type StationInfo struct {
	AirportList      []AirportPOIInfo      `json:"airportList"`
	TrainStationList []TrainStationPOIInfo `json:"trainStationList"`
	BusStationList   []BusStationPOIInfo   `json:"busStationList"`
}
type AirportPOIInfo struct {
	AirportCode         string                   `json:"airportCode"` // 机场三字码
	AirportName         string                   `json:"airportName"`
	AirportEnName       string                   `json:"airportEnName"`
	AirportBuildingList []AirportBuildingPOIInfo `json:"airportBuildingList"`
	// 机场类型，为null或者为空: ⺠⽤机场，特殊标识的类型，可能多个类型，⽤英⽂逗号分隔）：1: 通⽤机场 2: ⽆效废⻋站/停机坪/城市等 4: 军⽤机场
	AirportTypeList []string `json:"airportTypeList"`
}

type AirportBuildingPOIInfo struct {
	BuildingID     int64  `json:"buildingId"` // 航站楼 id
	BuildingName   string `json:"buildingName"`
	BuildingEnName string `json:"buildingEnName"`
	ShortName      string `json:"shortName"`
	ShortNameEn    string `json:"shortNameEn"`
	SmsName        string `json:"smsName"`
}
type TrainStationPOIInfo struct {
	TrainCode   string `json:"trainCode"`
	TrainName   string `json:"trainName"`
	TrainEnName string `json:"trainEnName"`
}
type BusStationPOIInfo struct {
	BusName       string `json:"busName"`
	BusPinYinName string `json:"busPinYinName"`
}
type CountyLevelCityPOI struct {
	CountyID     int64       `json:"countyId"`
	CountyName   string      `json:"countyName"`
	CountyEnName string      `json:"countyEnName"`
	CorpTag      int64       `json:"corpTag"`
	StationInfo  StationInfo `json:"stationInfo"`
	CountyCode   string      `json:"countyCode"`
	CountyPinyin string      `json:"countyPinyin"`
}
type DistrictPOIInfo struct {
	DistrictID     int64  `json:"districtId"`
	DistrictName   string `json:"districtName"`
	DistrictEnName string `json:"districtEnName"`
}

// Dida data structures for geography data

// DidaCountriesFile represents the structure of Dida countries JSON file
type DidaCountriesFile struct {
	Countries []DidaCountryInfo `json:"countries"`
}

// DidaCountryInfo represents a country from Dida
type DidaCountryInfo struct {
	Code     string `json:"code"`     // Country code like "CN", "US"
	Name     string `json:"name"`     // Country name like "China", "United States"
	LongName string `json:"longName"` // Optional full name
	ID       string `json:"id"`       // Dida internal ID
}

// DidaDestinationsFile represents the structure of Dida destinations JSON file
type DidaDestinationsFile struct {
	Country      DidaCountryInfo       `json:"country"`
	Destinations []DidaDestinationInfo `json:"destinations"`
}

// DidaDestinationInfo represents a destination from Dida
type DidaDestinationInfo struct {
	Code            string `json:"code"`            // Destination code
	Name            string `json:"name"`            // Destination name
	LongName        string `json:"longName"`        // Full hierarchical name like "Beijing, China, Asia"
	CountryCode     string `json:"countryCode"`     // Country code
	DestinationCode string `json:"destinationCode"` // Optional destination code
	ID              string `json:"id"`              // Dida internal ID
}

// ToRegion converts DidaCountryInfo to Region
func (d *DidaCountryInfo) ToRegion() *Region {
	return &Region{
		ID:       types.ID(idgen.String2Int64(d.Code + "_dida_country")),
		Type:     RegionType_Country,
		Name:     d.Name,
		NameFull: d.Name,
		SupplierID: SupplierID{
			DidaId: d.Code, // Use code as DidaId since ID field doesn't exist in actual files
		},
		CountryCode:            d.Code,
		CountrySubdivisionCode: "",
		Coordinates:            Coordinates{},
		Ancestors:              nil,
		Descendants:            nil,
		Extra: Extra{
			NameZh: d.Name, // For now, use the same name. Can be enhanced with multilingual support
		},
	}
}

// ToRegion converts DidaDestinationInfo to Region
func (d *DidaDestinationInfo) ToRegion(countryRegion *Region) *Region {
	// Determine region type to match Ctrip's type system
	regionType := d.determineRegionType()

	region := &Region{
		ID:       types.ID(idgen.String2Int64(d.Code)),
		Type:     regionType,
		Name:     d.Name, // This will be English name for en-US files, Chinese name for zh-CN files
		NameFull: d.LongName,
		SupplierID: SupplierID{
			DidaId: d.Code, // Use code as DidaId since ID field doesn't exist in actual files
		},
		CountryCode:            d.CountryCode,
		CountrySubdivisionCode: "",
		Coordinates:            Coordinates{},
		Ancestors:              []*Region{countryRegion},
		Descendants:            nil,
		Extra: Extra{
			NameZh:      "", // Will be set during multilingual merge
			CountryName: countryRegion.Name,
		},
	}
	// 在数据构建时优化NameFull
	region.OptimizeNameFull()
	return region
}

// determineRegionType intelligently determines the region type based on data characteristics
func (d *DidaDestinationInfo) determineRegionType() RegionType {
	// First check if it's obviously a county/district (override Dida's incorrect province marking)
	if d.isCountyLevel() {
		return RegionType_City
	}

	// Check if it's a province/state level region
	if d.isProvinceLevel() {
		return RegionType_ProvinceState
	}

	// Be conservative: only major cities should be MultiCityVicinity
	// Most regions should be City to match Ctrip's distribution
	if d.isTrueMajorCity() {
		return RegionType_MultiCityVicinity
	}

	// Default to City for most cities and districts (matches Ctrip's pattern)
	return RegionType_City
}

// isProvinceLevel checks if this destination represents a province/state
func (d *DidaDestinationInfo) isProvinceLevel() bool {
	// Chinese provinces
	chineseProvinces := map[string]bool{
		"anhui": true, "fujian": true, "gansu": true, "guangdong": true, "guangxi": true,
		"guizhou": true, "hainan": true, "hebei": true, "heilongjiang": true, "henan": true,
		"hubei": true, "hunan": true, "jiangsu": true, "jiangxi": true, "jilin": true,
		"liaoning": true, "qinghai": true, "shaanxi": true, "shandong": true, "shanxi": true,
		"sichuan": true, "yunnan": true, "zhejiang": true, "xinjiang": true, "tibet": true,
		"inner mongolia": true, "ningxia": true,
	}

	// Check if the name matches a known province
	nameLower := strings.ToLower(d.Name)
	if chineseProvinces[nameLower] {
		return true
	}

	// Check if LongName structure suggests province level (only country and province)
	if strings.Contains(d.LongName, ",") {
		parts := strings.Split(d.LongName, ",")
		if len(parts) == 2 {
			// Trim spaces from all parts
			provincePart := strings.TrimSpace(parts[0])
			countryPart := strings.TrimSpace(parts[1])

			// Format: "Province, Country" suggests province level
			// Make sure both parts are meaningful (not empty after trimming)
			if provincePart != "" && countryPart != "" {
				return true
			}
		}
	}

	return false
}

// isTrueMajorCity uses conservative heuristics to identify only true major cities
func (d *DidaDestinationInfo) isTrueMajorCity() bool {
	// Only direct municipalities should be MultiCityVicinity
	if d.isDirectMunicipality() {
		return true
	}

	// Only cities with "(and vicinity)" suffix should be MultiCityVicinity
	if d.hasVicinityIndicator() {
		return true
	}

	// Be very conservative - most cities should be Type:6 to match Ctrip
	return false
}

// isAdministrativeCity checks if this is an administrative city based on LongName structure
func (d *DidaDestinationInfo) isAdministrativeCity() bool {
	// If LongName has exactly 2 parts (City, Province), it's likely a prefecture-level city
	if strings.Contains(d.LongName, ",") {
		parts := strings.Split(d.LongName, ",")
		if len(parts) == 2 {
			// Format: "City, Province" suggests prefecture-level city
			cityPart := strings.TrimSpace(parts[0])
			provincePart := strings.TrimSpace(parts[1])

			// Make sure both parts are meaningful
			if cityPart == "" || provincePart == "" {
				return false
			}

			// If city name doesn't contain "County" or "District", it's likely a major city
			if !strings.Contains(cityPart, "County") &&
				!strings.Contains(cityPart, "District") &&
				!strings.Contains(cityPart, "Town") &&
				!strings.Contains(cityPart, "Village") {
				return true
			}
		}
	}
	return false
}

// isDirectMunicipality checks for direct municipalities
func (d *DidaDestinationInfo) isDirectMunicipality() bool {
	nameLower := strings.ToLower(d.Name)
	// Remove common suffixes
	nameLower = strings.TrimSuffix(nameLower, " (and vicinity)")
	nameLower = strings.TrimSuffix(nameLower, " (及周边地区)")
	nameLower = strings.TrimSuffix(nameLower, " (及其周边地区)")

	// Direct municipalities
	directMunicipalities := []string{"beijing", "shanghai", "tianjin", "chongqing"}
	for _, dm := range directMunicipalities {
		if nameLower == dm {
			return true
		}
	}
	return false
}

// isPrefectureLevelCity checks if this is a prefecture-level city
func (d *DidaDestinationInfo) isPrefectureLevelCity() bool {
	// If the name doesn't contain typical county/district/town indicators,
	// and it's not a province, it's likely a prefecture-level city
	nameLower := strings.ToLower(d.Name)

	// Exclude county-level indicators
	countyIndicators := []string{"county", "district", "town", "village", "township",
		"banner", "autonomous", "special", "new area", "development zone"}

	for _, indicator := range countyIndicators {
		if strings.Contains(nameLower, indicator) {
			return false
		}
	}

	// If it has a clean city name without administrative suffixes, likely prefecture-level
	return true
}

// hasVicinityIndicator checks if the name contains vicinity indicators
func (d *DidaDestinationInfo) hasVicinityIndicator() bool {
	nameLower := strings.ToLower(d.Name)

	// Only vicinity indicators should make it MultiCityVicinity
	vicinityIndicators := []string{
		"(and vicinity)", "(及周边地区)", "(及其周边地区)",
	}

	for _, indicator := range vicinityIndicators {
		if strings.Contains(nameLower, indicator) {
			return true
		}
	}

	return false
}

// isCountyLevel checks if this is obviously a county-level region that Dida incorrectly marked as province
func (d *DidaDestinationInfo) isCountyLevel() bool {
	name := d.Name

	// Obvious county indicators in the name
	countyIndicators := []string{
		"County", "县",
		"District", "区",
		"Banner", "旗",
		"Autonomous County", "自治县",
		"City District", "市辖区",
	}

	for _, indicator := range countyIndicators {
		if strings.Contains(name, indicator) {
			return true
		}
	}

	// Check LongName for county-level structure
	// Format like "County, City, Province" suggests county level
	if strings.Contains(d.LongName, ",") {
		parts := strings.Split(d.LongName, ",")
		if len(parts) >= 3 {
			// If there are 3+ parts, the first is likely a county/district
			firstPart := strings.TrimSpace(parts[0])
			for _, indicator := range countyIndicators {
				if strings.Contains(firstPart, indicator) {
					return true
				}
			}
		}
	}

	// Known county names that Dida incorrectly marks as province
	knownCounties := []string{
		"Wuxi County", "Liangping", "Mei County", "Feng County", "Yu County",
		"Shizhu County", "Pengshui County", "Wulong", "Fuling",
	}

	for _, county := range knownCounties {
		if name == county {
			return true
		}
	}

	return false
}
