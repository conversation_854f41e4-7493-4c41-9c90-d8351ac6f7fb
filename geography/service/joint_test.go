package service

import (
	"context"
	"fmt"
	"net/url"
	"os"
	"path"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/samber/lo"

	"gopkg.in/resty.v1"

	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"

	"hotel/common/config"
	"hotel/common/errgroup"
	"hotel/common/i18n"
	"hotel/common/types"
	"hotel/common/utils"
	"hotel/geography/domain"
	"hotel/geography/mysql"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

func getRepo() *mysql.RegionDao {
	dsn := "root:123456@tcp(localhost:3306)/hoteldev"
	conn := sqlx.NewMysql(dsn)
	return mysql.NewRegionDao(conn)
}
func UnifyCountryName(a string) string {
	switch a {
	case "Chinese Mainland":
		return "China"
	default:
		return a
	}
}

type TrainDataItem struct {
	SearchTerm         string      `json:"searchTerm"`
	CityFullName       string      `json:"cityFullName"`    // cityName,countryName,id
	BleveMatchScore    float64     `json:"textMatchScore"`  // --> bleve match score
	TravelRequestCount int         `json:"travelRequestUv"` // --> uniq user as destination
	TargetRank         int         `json:"targetRank"`
	CityName           string      `json:"cityName"`
	CountryName        string      `json:"countryName"`
	CtripCityID        string      `json:"ctripCityId"`
	loaded             atomic.Bool `json:"-"`
}

func (t *TrainDataItem) Headers() []string {
	return utils.GetJSONTags(t)
}

func (t *TrainDataItem) Match(cityName, provinceName, countryName string) bool {
	vs := strings.Fields(cityName)
	return strings.EqualFold(t.CountryName, countryName) && (strings.EqualFold(t.CityName, cityName) || strings.EqualFold(t.CityName, vs[0]) && strings.EqualFold(t.CountryName, countryName))
}

func TestBleveSearch(t *testing.T) {
	data := utils.ReadCSVFile("/Users/<USER>/Downloads/ctrip_hotel_city.csv")
	indexManager := NewInMemoryIndexManager()
	ctripHotelCities := make([]*domain.DeprecatedCtripHotelCity, 0)
	for _, row := range data {
		ctripHotelCities = append(ctripHotelCities, (&domain.DeprecatedCtripHotelCity{}).FromDeprecatedBDCtripHotelCityStringMap(row))
	}
	indexManager.BuildIndexesFromCtripHotelCity(ctripHotelCities)

	//regions := indexManager.GetAllRegionList()
	searchService, err := NewBleveSearchService(indexManager)
	if err != nil {
		t.Fatalf("NewBleveSearchService %v", err)
	}
	ctx := context.Background()

	res1, res, err := searchService.Search(ctx, "Shanghai", 1, 1)
	t.Logf("%v %#v\n", err, res)
	convey.ShouldEqual(res1[0].Name, "Shanghai")
	res1, _, _ = searchService.Search(ctx, "new york", 1, 1)
	convey.ShouldEqual(res1[0].Name, "New York")
	res1, _, _ = searchService.Search(ctx, "newyork", 1, 1)
	convey.ShouldEqual(res1[0].Name, "New York")
	res1, _, _ = searchService.Search(ctx, "纽约", 1, 1)
	convey.ShouldEqual(res1[0].Name, "New York")

	// fuzziness
	res1, _, _ = searchService.Search(ctx, "Shaghai", 1, 1)
	convey.ShouldEqual(res1[0].Name, "Shanghai")
	res1, _, _ = searchService.Search(ctx, "new yrk", 1, 1)
	convey.ShouldEqual(res1[0].Name, "New York")
}

type RegionWrapper struct {
	*domain.Region
	TravelRequestUV int
	TextMatchScore  float64
}

var (
	httpCli = resty.New()
)

// 主结构体
type CityInfo struct {
	ProvinceName            string          `json:"provinceName"`
	CountryName             string          `json:"countryName"`
	ProvinceNameEn          string          `json:"provinceNameEn"`
	CountryNameEn           string          `json:"countryNameEn"`
	IsDomestic              bool            `json:"isDomestic"`
	CityNameDisplay         string          `json:"cityNameDisplay"`
	ProvinceNameDisplay     string          `json:"provinceNameDisplay"`
	CountryNameDisplay      string          `json:"countryNameDisplay"`
	CityNameSpell           string          `json:"cityNameSpell"`
	ShortCityNameSpell      string          `json:"shortCityNameSpell"`
	ProvinceRegionCode      string          `json:"provinceRegionCode"`
	CountryRegionCode       string          `json:"countryRegionCode"`
	CityLanguageMapping     LanguageMapping `json:"cityLanguageMapping"`
	ProvinceLanguageMapping LanguageMapping `json:"provinceLanguageMapping"`
	CountryLanguageMapping  LanguageMapping `json:"countryLanguageMapping"`
	CityId                  string          `json:"cityId"`
	CityName                string          `json:"cityName"`
	CityNameEn              string          `json:"cityNameEn"`
	TextMatchScore          float64         `json:"_textMatchScore"`
	Formula                 string          `json:"_formula"` // 如需解析 formula 中的 JSON，建议改为 map 类型
	FinalScore              float64         `json:"_finalScore"`
}

// 语言映射结构体
type LanguageMapping struct {
	En string `json:"en"`
	Zh string `json:"zh"`
}

type CityFuzzySearchResp struct {
	Data []CityInfo `json:"data"`
}
type Formula struct {
	TextMatchWeight       float64 `json:"textMatchWeight"`
	TravelRequestUV       int     `json:"travelRequestUv"`
	TravelRequestUVWeight float64 `json:"travelRequestUvWeight"`
}

var (
	costTime   []int
	costTimeMu sync.Mutex
)

// 在 CityInfo 中使用该结构体：
// Formula Formula `json:"formula"`
func searchTravelCityFuzzySearch(ctx context.Context, t string) ([]*RegionWrapper, error) {
	st := time.Now()
	resp, err := httpCli.R().SetQueryString("fuzzy=" + url.QueryEscape(t)).SetHeaders(map[string]string{
		"x-use-ppe": "1",
		"x-tt-env":  "ppe_i18n_env_hotellist",
		"x-lang":    "zh101",
	}).Get("http://travel-pre.bytedance.net/api/bff/geo/cityFuzzySearch")
	if err != nil {
		return nil, err
	}
	costTimeMu.Lock()
	costTime = append(costTime, int(time.Since(st).Milliseconds()))
	costTimeMu.Unlock()
	var r = new(CityFuzzySearchResp)
	if err = sonic.Unmarshal(resp.Body(), r); err != nil {
		return nil, err
	}

	out := make([]*RegionWrapper, 0)
	for _, v := range r.Data {
		form := &Formula{}
		sonic.UnmarshalString(v.Formula, form)
		out = append(out, &RegionWrapper{
			Region: &domain.Region{
				ID:         types.ID(cast.ToInt64(v.CityId)),
				Name:       v.CityNameEn,
				SupplierID: domain.SupplierID{},
				Extra: domain.Extra{
					NameZh:       v.CityName,
					CountryName:  v.CountryNameEn,
					ProvinceName: v.ProvinceNameEn,
				},
			},
			TravelRequestUV: form.TravelRequestUV,
			TextMatchScore:  v.TextMatchScore,
		})
	}
	return out, nil
}

func BenchmarkTestCityUserClick(t *testing.B) {
	ctx := context.Background()
	userClicks := utils.ReadCSVFile(path.Join(config.GetProjectPath(), "geography/train/city_user_click.csv"))
	good, bad, same, notFound := 0, 0, 0, 0
	engood, enbad, ensame, enNotFound := 0, 0, 0, 0
	zhgood, zhbad, zhsame, zhNotFound := 0, 0, 0, 0
	rankBenefit := make([]int, 0)
	var mu sync.Mutex
	wg := errgroup.WithContext(ctx)
	wg.GOMAXPROCS(2)
	for _, v := range userClicks {
		v := v
		term := v["queryName"]
		iszh := i18n.AllChineseOrJapanese(term)
		selectedCityIndex := cast.ToInt(v["selectedCityIndex"])
		selectedCityName := v["selectedCityName"]
		selectedCityNameEn := v["selectedCityNameEn"]
		selectedCityID := cast.ToInt64(v["selectedCityId"])
		wg.Go(func(ctx context.Context) error {
			candidates, err := searchTravelCityFuzzySearch(ctx, term)
			if err != nil {
				panic(err)
			}
			found := false

			mu.Lock()
			defer mu.Unlock()
			for i, c := range candidates {
				if c.ID.Int64() == selectedCityID {
					found = true
					rankBenefit = append(rankBenefit, selectedCityIndex-i)
					if i < selectedCityIndex {
						fmt.Printf("good case %s, searched at %v < %v,  %s %s \n", term, i, selectedCityIndex, selectedCityName, selectedCityNameEn)
						good++
						if iszh {
							zhgood++
						} else {
							engood++
						}
					} else if i > selectedCityIndex {
						fmt.Printf("bad case %s, searched at %v > %v,  %s %s \n", term, i, selectedCityIndex, selectedCityName, selectedCityNameEn)
						bad++
						if iszh {
							zhbad++
						} else {
							enbad++
						}
					} else {
						same++
						if iszh {
							zhsame++
						} else {
							ensame++
						}
					}
					break
				}
			}
			if !found {
				fmt.Printf("not found %s, %s \n", term, utils.ToJSON(v))
				notFound++
				if iszh {
					zhNotFound++
				} else {
					enNotFound++
				}
			}
			return nil
		})
	}
	wg.Wait()
	fmt.Printf("total:%d, good:%d, bad:%d, same:%d, not found:%d\n", good+bad+same, good, bad, same, notFound)
	fmt.Printf("ZH total:%d, good:%d, bad:%d, same:%d, not found:%d\n", zhgood+zhbad+zhsame, zhgood, zhbad, zhsame, zhNotFound)
	fmt.Printf("EN total:%d, good:%d, bad:%d, same:%d, not found:%d\n", engood+enbad+ensame, engood, enbad, ensame, enNotFound)
	for _, p := range []int{50, 75, 90, 95, 99} {
		fmt.Printf("API Performance: p%d=%dms\n", p, utils.Percentile(costTime, p))
	}
	for _, p := range []int{50, 75, 90, 95, 99} {
		fmt.Printf("Rank Optimization: p%d=%d\n", p, utils.Percentile(rankBenefit, p))
	}
	fmt.Printf("%d ranks saved: %v\n", len(rankBenefit), lo.Sum(rankBenefit))
	fmt.Printf("%d ranks saved max: %v\n", len(rankBenefit), lo.Max(rankBenefit))
}

func TestTrainData(t *testing.T) {
	ctx := context.Background()
	targetRank := utils.ReadCSVFile(path.Join(config.GetProjectPath(), "geography/train/target_rank.csv"))
	var records []interface{}
	for _, v := range targetRank {
		term := v["Query"]
		tdi := &TrainDataItem{
			SearchTerm:   term,
			CityFullName: v["CityName"] + "," + UnifyCountryName(v["CountryName"]),
			TargetRank:   cast.ToInt(v["Rank"]),
			CityName:     v["CityName"],
			CountryName:  UnifyCountryName(v["CountryName"]),
		}

		candidates, err := searchTravelCityFuzzySearch(ctx, term)
		if err != nil {
			panic(err)
		}
		if len(candidates) == 0 {
			fmt.Printf("%s has no candidates\n", term)
		} else {
			fmt.Printf("%s has %d candidates\n", term, len(candidates))
		}
		for _, candidate := range candidates {
			var lastMatch *RegionWrapper
			if tdi.Match(candidate.Name, candidate.Extra.ProvinceName, candidate.Extra.CountryName) {
				if tdi.loaded.Load() {
					logstr := utils.ToJSON(map[string]interface{}{
						"term":                 term,
						"last_match_score":     tdi.BleveMatchScore,
						"train_data":           tdi,
						"cur_match_candidate":  candidate,
						"last_match_candidate": lastMatch,
					})
					fmt.Printf("%s\n", logstr)

				}
				tdi.loaded.Store(true)
				tdi.BleveMatchScore = candidate.TextMatchScore
				tdi.TravelRequestCount = candidate.TravelRequestUV
				//if vv := findCity(cast.ToString(candidate.SupplierHotelId)); vv != candidate.TravelRequestUV {
				//	fmt.Printf("travelRequestUV not equal %v %v != %v\n", candidate.SupplierHotelId, candidate.TravelRequestUV, vv)
				//}
				tdi.CtripCityID = candidate.TripId
				lastMatch = candidate
			}
		}

		if !tdi.loaded.Load() {
			fmt.Printf("not found: %s -> %s,%s\n", term, tdi.CityName, tdi.CountryName)
		} else {
			fmt.Printf("found: %v, got %s %s\n", term, tdi.CityName, tdi.CountryName)
		}
		records = append(records, tdi)
		fmt.Printf("records: %v\n", len(records))
	}
	fn := path.Join(config.GetProjectPath(), "geography/train/train_data.csv")
	if err := utils.WriteStructsToCSV(records, (&TrainDataItem{}).Headers(), fn); err != nil {
		panic(err)
	}
	for _, p := range []int{50, 75, 90, 95, 99} {
		fmt.Printf("p%d=%dms\n", p, utils.Percentile(costTime, p))
	}
}

func TestTransformCtripAllPOI(t *testing.T) {
	repo := getRepo()
	regions, err := repo.LoadAllRegionsFromFile(context.Background())
	if err != nil {
		panic(err)
	}
	indexManager := NewInMemoryIndexManager()
	indexManager.BuildIndexes(regions)
	regions2 := indexManager.GetAllRegionList()
	t.Logf("regions: %v\n", len(regions2))
	_, err = NewBleveSearchService(indexManager)
	if err != nil {
		panic(err)
	}
}

func TestTransformCtripCityData(t *testing.T) {
	repo := getRepo()
	data := utils.ReadCSVFile(os.Getenv("FN"))
	indexManager := NewInMemoryIndexManager()
	ctripHotelCities := make([]*domain.DeprecatedCtripHotelCity, 0)
	for _, row := range data {
		ctripHotelCities = append(ctripHotelCities, (&domain.DeprecatedCtripHotelCity{}).FromStringMap(row))
	}
	indexManager.BuildIndexesFromCtripHotelCity(ctripHotelCities)
	regions := indexManager.GetAllRegionList()
	if err := repo.Save(context.Background(), regions...); err != nil {
		t.Fatalf("count(%d) err(%+v)", len(regions), err)
	}
}

func TestBleve(t *testing.T) {
	indexManager := NewInMemoryIndexManager()
	indexManager.BuildIndexes([]*domain.Region{})

	// 初始化搜索服务
	_, err := NewBleveSearchService(indexManager)
	if err != nil {
		t.Fatalf("NewBleveSearchService failed: %v", err)
	}
}

func TestInMemorySearch(t *testing.T) {
	repo := getRepo()

	// 加载数据
	regions, err := repo.MonitoredLoadAllRegions(context.Background())
	if err != nil {
		t.Fatalf("LoadAllRegions %v", err)
	}
	// 构建索引
	indexManager := NewInMemoryIndexManager()
	indexManager.BuildIndexes(regions)

	// 初始化搜索服务
	searchService, err := NewBleveSearchService(indexManager)
	if err != nil {
		t.Fatalf("NewBleveSearchService %v", err)
	}
	// 初始化层级服务
	hierarchyService := NewHierarchySearchService(indexManager)
	res := hierarchyService.GetDescendants(2)
	t.Logf("GetDescendants: %s\n", utils.ToLogString(res))
	// 后续可注入到业务逻辑中...
	res, score, err := searchService.Search(context.Background(), "上海", 1, 10)
	t.Logf("Anything: %v %v %v\n", utils.ToJSON(res), score, err)
}
