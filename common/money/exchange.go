package money

import (
	"context"
	"errors"
	"hotel/common/httphelper"
	"log"
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

type ExchangeService interface {
	// Exchange 先统一用 Decimal 类型
	Exchange(ctx context.Context, from, to string, amount decimal.Decimal) (decimal.Decimal, error)
}

// https://github.com/fawazahmed0/exchange-api
// https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@{date}/{apiVersion}/{endpoint}
//The Endpoints Supports HTTP GET Method and returns the data in two formats:
//
///{endpoint}.json
///{endpoint}.min.json
// #### Endpoints:
//
//- `/currencies`<br>
//> Lists all the available currencies in prettified json format:<br>
// https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies.json <br>
//
//> Get a minified version of it:<br>
//https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies.min.json <br>
//
//- `/currencies/{currencyCode}`<br>
//> Get the currency list with EUR as base currency:<br>
//https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/eur.json <br>
//
//> Get the currency list with EUR as base currency on date 2024-03-06:<br>
//https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@2024-03-06/v1/currencies/eur.json <br>
//
//> Get the currency list with BTC as base currency:<br>
//https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/btc.json <br>
//
//> Get the currency list with BTC as base currency in minified format:<br>
//https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/btc.min.json <br>
//
//#### Additional Fallback URL on Cloudflare:
//
//`https://{date}.currency-api.pages.dev/{apiVersion}/{endpoint}`
//
//> Get the currency list with EUR as base currency:<br>
//https://latest.currency-api.pages.dev/v1/currencies/eur.json
//
//> Get the currency list with EUR as base currency on date 2024-03-06:<br>
//https://2024-03-06.currency-api.pages.dev/v1/currencies/eur.json
//
//**Warning:** Please include [Fallback mechanism](https://github.com/fawazahmed0/exchange-api/issues/90#issue-2168885277) in your code, for example if `cdn.jsdelivr.net` link fails, fetch from `currency-api.pages.dev`
//
//**Migrating from Previous Currency API:** [Read this](https://github.com/fawazahmed0/exchange-api/blob/main/MIGRATION.md)

// ExchangeServiceImpl @qiutian
type ExchangeServiceImpl struct {
	cache     sync.Map
	cacheTTL  time.Duration
	lastFetch time.Time
	fetchLock sync.Mutex
}

// ExchangeCacheItem 缓存项
type ExchangeCacheItem struct {
	Data      *ExchangeInfo
	Timestamp time.Time
}

func NewExchangeService() *ExchangeServiceImpl {
	return &ExchangeServiceImpl{
		cacheTTL: 1 * time.Hour, // 缓存1小时
	}
}

func (s *ExchangeServiceImpl) Exchange(ctx context.Context, from, to string, amount decimal.Decimal) (decimal.Decimal, error) {
	exchangeInfo, err := s.getExchangeInfoWithCache(ctx)
	if err != nil {
		return decimal.Decimal{}, err
	}
	fromRate, ok1 := exchangeInfo.Usd[from]
	toRate, ok2 := exchangeInfo.Usd[to]
	if !ok1 || !ok2 {
		return decimal.Decimal{}, errors.New("unknown currencies")
	}

	return amount.Div(fromRate).Mul(toRate), nil
}

// getExchangeInfoWithCache 带缓存的汇率信息获取
func (s *ExchangeServiceImpl) getExchangeInfoWithCache(ctx context.Context) (*ExchangeInfo, error) {
	// 检查缓存
	if cached, ok := s.cache.Load("exchange_info"); ok {
		if cacheItem, ok := cached.(*ExchangeCacheItem); ok {
			// 检查缓存是否过期
			if time.Since(cacheItem.Timestamp) < s.cacheTTL {
				return cacheItem.Data, nil
			}
		}
	}

	// 缓存过期或不存在，需要重新获取
	return s.fetchAndCacheExchangeInfo(ctx)
}

// fetchAndCacheExchangeInfo 获取并缓存汇率信息
func (s *ExchangeServiceImpl) fetchAndCacheExchangeInfo(ctx context.Context) (*ExchangeInfo, error) {
	s.fetchLock.Lock()
	defer s.fetchLock.Unlock()

	// 双重检查，防止并发时重复获取
	if cached, ok := s.cache.Load("exchange_info"); ok {
		if cacheItem, ok := cached.(*ExchangeCacheItem); ok {
			if time.Since(cacheItem.Timestamp) < s.cacheTTL {
				return cacheItem.Data, nil
			}
		}
	}

	// 获取新的汇率信息
	exchangeInfo, err := getExchangeInfo(ctx)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	cacheItem := &ExchangeCacheItem{
		Data:      exchangeInfo,
		Timestamp: time.Now(),
	}
	s.cache.Store("exchange_info", cacheItem)
	s.lastFetch = time.Now()

	return exchangeInfo, nil
}

// ClearCache 清除缓存
func (s *ExchangeServiceImpl) ClearCache() {
	s.cache.Delete("exchange_info")
}

// GetCacheStatus 获取缓存状态
func (s *ExchangeServiceImpl) GetCacheStatus() map[string]interface{} {
	status := map[string]interface{}{
		"lastFetch": s.lastFetch,
		"cacheTTL":  s.cacheTTL,
	}

	if cached, ok := s.cache.Load("exchange_info"); ok {
		if cacheItem, ok := cached.(*ExchangeCacheItem); ok {
			status["hasCache"] = true
			status["cacheAge"] = time.Since(cacheItem.Timestamp)
			status["isExpired"] = time.Since(cacheItem.Timestamp) >= s.cacheTTL
		}
	} else {
		status["hasCache"] = false
	}

	return status
}

type ExchangeInfo struct {
	Date string                     `json:"date"`
	Usd  map[string]decimal.Decimal `json:"usd"`
}

func getExchangeInfo(ctx context.Context) (*ExchangeInfo, error) {
	// 创建客户端
	client := httphelper.New(
		httphelper.WithBaseURL("https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies"),
		httphelper.WithTimeout(10*time.Second),
		httphelper.WithDefaultHeader("Authorization", "Bearer TOKEN"),
		httphelper.WithRetry(3),
	)

	// GET请求带查询参数
	resp, err := client.Get("/usd.json", nil, nil)
	if err != nil {
		log.Printf("call http error. err:%v", err)
		return nil, err
	}

	if !resp.IsSuccess() {
		log.Printf("Request failed with status: %d\n", resp.StatusCode)
		return nil, errors.New("Request failed")
	}
	// 处理成功响应
	var exchangeInfo ExchangeInfo
	if err := resp.JSON(&exchangeInfo); err != nil {
		log.Printf("json Unmarshal error.%v", err)
		return nil, err
	}

	return &exchangeInfo, nil
}
