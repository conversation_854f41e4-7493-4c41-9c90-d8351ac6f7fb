package dida

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"hotel/supplier/dida/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDidaClient_GetCountries tests the GetCountries API
func TestDidaClient_GetCountries(t *testing.T) {
	client := getTestClient(t)
	ctx := context.Background()

	// Test with default language (English)
	resp, err := client.GetCountries(ctx, "")
	require.NoError(t, err)
	require.NotNil(t, resp)

	assert.NotEmpty(t, resp.TraceID)
	assert.NotEmpty(t, resp.Timestamp)
	assert.NotEmpty(t, resp.Data)

	t.Logf("Retrieved %d countries", len(resp.Data))

	// Check first few countries
	for i, country := range resp.Data {
		if i >= 5 {
			break
		}
		assert.NotEmpty(t, country.Code)
		assert.NotEmpty(t, country.Name)
		t.Logf("Country %d: %s (%s)", i+1, country.Name, country.Code)
	}
}

// TestDidaClient_GetDestinations tests the GetDestinations API
func TestDidaClient_GetDestinations(t *testing.T) {
	client := getTestClient(t)
	ctx := context.Background()

	// Test with China (CN)
	resp, err := client.GetDestinations(ctx, "CN", "en-US")
	require.NoError(t, err)
	require.NotNil(t, resp)

	assert.NotEmpty(t, resp.TraceID)
	assert.NotEmpty(t, resp.Timestamp)
	assert.NotEmpty(t, resp.Data)

	t.Logf("Retrieved %d destinations for China", len(resp.Data))

	// Check first few destinations
	for i, dest := range resp.Data {
		if i >= 5 {
			break
		}
		assert.NotEmpty(t, dest.Code)
		assert.NotEmpty(t, dest.Name)
		assert.Equal(t, "CN", dest.CountryCode)
		t.Logf("Destination %d: %s (%s)", i+1, dest.Name, dest.Code)
	}
}

// TestDidaClient_GetAllCountryInfo downloads all geography data and saves to local files
// This test only runs when ENV=DEV to avoid modifying config files in other environments
func TestDidaClient_GetAllCountryInfo(t *testing.T) {
	// Skip this test unless ENV=DEV
	if os.Getenv("ENV") != "DEV" {
		t.Skip("Skipping TestDidaClient_GetAllCountryInfo: only runs when ENV=DEV")
		return
	}

	client := getTestClient(t)
	ctx := context.Background()

	// Create output directory
	outputDir := "../../geography/config/dida"
	err := os.MkdirAll(outputDir, 0755)
	require.NoError(t, err)

	// Test with multiple languages
	languages := []string{"en-US", "zh-CN"}

	for _, language := range languages {
		t.Run(fmt.Sprintf("Language_%s", language), func(t *testing.T) {
			t.Logf("Downloading all geography data for language: %s", language)

			// Get all geography data
			allData, err := client.GetAllGeographyData(ctx, language)
			require.NoError(t, err)
			require.NotNil(t, allData)

			t.Logf("Downloaded data summary: Countries=%d, Destinations=%d, Success=%d, Failed=%d",
				allData.Summary.TotalCountries, allData.Summary.TotalDestinations,
				allData.Summary.SuccessfulFetches, allData.Summary.FailedFetches)

			// Save complete data to file
			filename := fmt.Sprintf("all_geography_data_%s.json", language)
			filePath := filepath.Join(outputDir, filename)

			file, err := os.Create(filePath)
			require.NoError(t, err)
			defer file.Close()

			encoder := json.NewEncoder(file)
			encoder.SetIndent("", "  ")
			err = encoder.Encode(allData)
			require.NoError(t, err)

			t.Logf("Saved complete geography data to: %s", filePath)

			// Save countries only
			countriesFilename := fmt.Sprintf("countries_%s.json", language)
			countriesFilePath := filepath.Join(outputDir, countriesFilename)

			countriesData := make([]model.Country, 0, len(allData.Countries))
			for _, countryWithDest := range allData.Countries {
				countriesData = append(countriesData, countryWithDest.Country)
			}

			countriesFile, err := os.Create(countriesFilePath)
			require.NoError(t, err)
			defer countriesFile.Close()

			countriesEncoder := json.NewEncoder(countriesFile)
			countriesEncoder.SetIndent("", "  ")
			err = countriesEncoder.Encode(map[string]interface{}{
				"fetchedAt": time.Now().Format(time.RFC3339),
				"language":  language,
				"total":     len(countriesData),
				"countries": countriesData,
			})
			require.NoError(t, err)

			t.Logf("Saved countries data to: %s", countriesFilePath)

			// Save destinations by country
			for _, countryWithDest := range allData.Countries {
				if len(countryWithDest.Destinations) == 0 {
					continue
				}

				destFilename := fmt.Sprintf("destinations_%s_%s_%s.json",
					countryWithDest.Country.Code, countryWithDest.Country.Name, language)
				// Clean filename
				destFilename = cleanFilename(destFilename)
				destFilePath := filepath.Join(outputDir, destFilename)

				destFile, err := os.Create(destFilePath)
				if err != nil {
					t.Logf("Warning: Failed to create file %s: %v", destFilePath, err)
					continue
				}

				destEncoder := json.NewEncoder(destFile)
				destEncoder.SetIndent("", "  ")
				err = destEncoder.Encode(map[string]interface{}{
					"country":      countryWithDest.Country,
					"destinations": countryWithDest.Destinations,
					"fetchedAt":    countryWithDest.FetchedAt,
					"language":     countryWithDest.Language,
					"total":        countryWithDest.TotalCount,
				})
				destFile.Close()

				if err != nil {
					t.Logf("Warning: Failed to encode destinations for %s: %v",
						countryWithDest.Country.Name, err)
					continue
				}

				if len(countryWithDest.Destinations) > 0 {
					t.Logf("Saved %d destinations for %s (%s) to: %s",
						len(countryWithDest.Destinations),
						countryWithDest.Country.Name,
						countryWithDest.Country.Code,
						destFilename)
				}
			}
		})
	}
}

// TestDidaClient_GetCountriesByLanguage tests multi-language support
func TestDidaClient_GetCountriesByLanguage(t *testing.T) {
	client := getTestClient(t)
	ctx := context.Background()

	languages := []string{"en-US", "zh-CN", "ja-JP"}

	result, err := client.GetCountriesByLanguage(ctx, languages)
	require.NoError(t, err)
	require.NotEmpty(t, result)

	for lang, countries := range result {
		assert.NotEmpty(t, countries.Data)
		t.Logf("Language %s: %d countries", lang, len(countries.Data))

		// Check first country in each language
		if len(countries.Data) > 0 {
			firstCountry := countries.Data[0]
			t.Logf("First country in %s: %s (%s)", lang, firstCountry.Name, firstCountry.Code)
		}
	}
}

// TestDidaClient_GetCountryByCode tests getting a specific country
func TestDidaClient_GetCountryByCode(t *testing.T) {
	client := getTestClient(t)
	ctx := context.Background()

	// Test getting China
	country, err := client.GetCountryByCode(ctx, "CN", "en-US")
	require.NoError(t, err)
	require.NotNil(t, country)

	assert.Equal(t, "CN", country.Code)
	assert.NotEmpty(t, country.Name)
	t.Logf("Found country: %s (%s)", country.Name, country.Code)

	// Test non-existent country
	_, err = client.GetCountryByCode(ctx, "INVALID", "en-US")
	assert.Error(t, err)
}

// TestDidaClient_LanguageValidation tests language validation
func TestDidaClient_LanguageValidation(t *testing.T) {
	client := getTestClient(t)
	ctx := context.Background()

	// Test valid languages
	validLanguages := []string{"en-US", "zh-CN", "ja-JP", "ko-KR", "pt-BR", "es-ES"}
	for _, lang := range validLanguages {
		_, err := client.GetCountries(ctx, lang)
		assert.NoError(t, err, "Language %s should be valid", lang)
	}

	// Test invalid language
	_, err := client.GetCountries(ctx, "invalid-lang")
	assert.Error(t, err, "Invalid language should return error")
}

// Helper function to get test client
func getTestClient(t *testing.T) *DidaClient {
	client := NewDidaClient()
	return client
}
