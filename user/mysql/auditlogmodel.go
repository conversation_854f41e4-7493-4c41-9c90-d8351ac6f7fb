package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"hotel/common/pagehelper"
	"time"

	"hotel/common/types"
	"hotel/common/utils"
	"hotel/user/domain"
)

type (
	AuditLogModel struct {
		*defaultAuditLogModel
	}
)

// NewAuditLogModel returns a model for the database table.
func NewAuditLogModel(conn sqlx.SqlConn) *AuditLogModel {
	return &AuditLogModel{
		defaultAuditLogModel: newAuditLogModel(conn),
	}
}

func (m *AuditLogModel) withSession(session sqlx.Session) *AuditLogModel {
	return NewAuditLogModel(sqlx.NewSqlConnFromSession(session))
}

func (m *AuditLogModel) QueryByActionTypeAndTime(ctx context.Context, actionType string, actionTimeStart, actionTimeEnd time.Time, pageReq pagehelper.PageReq) ([]*AuditLog, error) {
	var resp []*AuditLog
	// 使用 sqlx.In 处理数组参数
	baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE `action_type`=? AND `action_time`>? AND `action_time`<=? ORDER BY `action_time` DESC LIMIT ?,?", auditLogRows, m.table)

	err := m.conn.QueryRowsCtx(ctx, &resp, baseQuery, actionType, actionTimeStart, actionTimeEnd, pageReq.GetOffset(), pageReq.PageSize)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlx.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func convert2ModelAuditLog(in *domain.AuditLog) *AuditLog {
	if in == nil {
		return nil
	}
	return &AuditLog{
		Id:                 in.Id,
		ActionType:         in.ActionType,
		ActionTime:         in.ActionTime,
		ActorUserId:        in.ActorUserId.Int64(),
		ActorInfo:          in.ActorInfo,
		AffectedEntityId:   in.AffectedEntityId.Int64(),
		AffectedEntityInfo: in.AffectedEntityInfo,
		Details:            sql.NullString{String: utils.ToLogString(in.Details), Valid: true},
	}
}

func convert2DomainAuditLog(in *AuditLog) *domain.AuditLog {
	if in == nil {
		return nil
	}
	return &domain.AuditLog{
		Id:                 in.Id,
		ActionType:         in.ActionType,
		ActionTime:         in.ActionTime,
		ActorUserId:        types.ID(in.ActorUserId),
		ActorInfo:          in.ActorInfo,
		AffectedEntityId:   types.ID(in.AffectedEntityId),
		AffectedEntityInfo: in.AffectedEntityInfo,
		Details:            utils.FromJSON[domain.AuditLogDetail](in.Details.String),
	}
}
