package utils

import (
	"reflect"
	"sort"
	"strconv"
	"strings"
)

func Map[T, U any](s []T, f func(T) (U, error)) ([]U, error) {
	result := make([]U, len(s))
	for i, v := range s {
		var err error
		result[i], err = f(v)
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}
func Filter[T any](s []T, predicate func(T) bool) []T {
	result := make([]T, 0, len(s))
	for _, v := range s {
		if predicate(v) {
			result = append(result, v)
		}
	}
	return result
}
func Deduplicate[T comparable](s []T) []T {
	if len(s) == 0 {
		return s
	}
	seen := make(map[T]struct{})
	result := make([]T, 0, len(s))
	for _, v := range s {
		if _, ok := seen[v]; !ok {
			seen[v] = struct{}{}
			result = append(result, v)
		}
	}
	return result
}

// DeduplicateInt64Slice removes duplicate values from an int64 slice in place.
func DeduplicateInt64Slice(ids []int64) []int64 {
	if len(ids) == 0 {
		return ids
	}

	// 排序数组
	sort.Slice(ids, func(i, j int) bool { return ids[i] < ids[j] })

	// 原地去重
	j := 0
	for i := 1; i < len(ids); i++ {
		if ids[i] != ids[j] {
			j++
			ids[j] = ids[i]
		}
	}

	return ids[:j+1]
}

func Iterate(m interface{}, it func(k, v interface{}) (continueIteration bool)) {
	if m == nil {
		return
	}
	refval := reflect.ValueOf(m)
	k := refval.Kind()
	switch k {
	case reflect.Array, reflect.Slice:
		l := refval.Len()
		for i := 0; i < l; i++ {
			if !it(i, refval.Index(i).Interface()) {
				break
			}
		}
	case reflect.Map:
		r := refval.MapRange()
		for r.Next() {
			if !it(r.Key().Interface(), r.Value().Interface()) {
				break
			}
		}
	default:
	}
}

// Int64SliceRemoveDuplicatesBySort .
func Int64SliceRemoveDuplicatesBySort(nums []int64) []int64 {
	if len(nums) <= 1 {
		return nums
	}
	sort.Slice(nums, func(i, j int) bool {
		return nums[i] < nums[j]
	})
	index := 0
	for i := 1; i < len(nums); i++ {
		if nums[i] != nums[index] {
			index++
			nums[index] = nums[i]
		}
	}
	return nums[:index+1]
}

// IntSliceRemoveDuplicatesBySort .
func IntSliceRemoveDuplicatesBySort(nums []int) []int {
	if len(nums) <= 1 {
		return nums
	}
	sort.Slice(nums, func(i, j int) bool {
		return nums[i] < nums[j]
	})
	index := 0
	for i := 1; i < len(nums); i++ {
		if nums[i] != nums[index] {
			index++
			nums[index] = nums[i]
		}
	}
	return nums[:index+1]
}

func JoinInt64Slice(slice []int64, sep string) string {
	if len(slice) == 0 {
		return ""
	}
	builder := strings.Builder{}
	builder.Grow(len(slice) * 4) // 预分配内存（优化性能）
	builder.WriteString(strconv.FormatInt(slice[0], 10))
	for _, num := range slice[1:] {
		builder.WriteString(sep)
		builder.WriteString(strconv.FormatInt(num, 10))
	}
	return builder.String()
}
