package giata

import (
	"bytes"
	"context"
	"encoding/xml"
	"errors"
	"hotel/mapping/supplier/giata/model"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"

	"golang.org/x/net/html/charset"
)

type GiataClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func (s *GiataClient) Supplier() domain.Supplier {
	return domain.Supplier_Giata
}

// 获取单个酒店静态详情
func (s *GiataClient) HotelStaticDetail(ctx context.Context, req *domain.HotelStaticDetailReq) (*domain.HotelStaticDetailResp, error) {
	// GIATA API需要特定的查询参数格式
	giataReq := map[string]string{
		"sc":   "hotel",
		"vc":   "GIATA", // 供应商代码，需要根据实际情况调整
		"oc":   req.SupplierHotelId,
		"show": "fact,hn,vn",
	}
	// 用string接收原始响应
	var respXml string
	if err := s.Execute(ctx, domain.APIName_HotelStaticDetail, giataReq, &respXml); err != nil {
		return nil, err
	}
	// 用encoding/xml+charset.NewReaderLabel解码
	decoder := xml.NewDecoder(bytes.NewReader([]byte(respXml)))
	decoder.CharsetReader = charset.NewReaderLabel
	giataResp := new(model.HotelDetailsResponse)
	if err := decoder.Decode(giataResp); err != nil {
		return nil, err
	}
	convertedResult := ConvertHotelDetailsResponse2HotelListResp(giataResp)
	if len(convertedResult.Hotels) == 0 {
		return nil, nil
	}
	return &domain.HotelStaticDetailResp{
		HotelStaticProfile: convertedResult.Hotels[0].HotelStaticProfile,
	}, nil
}

// 批量获取酒店静态详情
func (s *GiataClient) batchHotelStaticDetail(ctx context.Context, supplierHotelIds []string) (*domain.BatchHotelStaticDetailResp, error) {
	// GIATA API需要逐个查询，暂不支持批量
	var hotels []domain.SupplierHotel
	for _, hotelId := range supplierHotelIds {
		detail, err := s.HotelStaticDetail(ctx, &domain.HotelStaticDetailReq{
			SupplierHotelId: hotelId,
		})
		if err != nil {
			continue // 跳过错误的酒店ID
		}
		if detail != nil {
			hotels = append(hotels, domain.SupplierHotel{
				SupplierHotelId:    hotelId,
				Supplier:           domain.Supplier_Giata,
				HotelStaticProfile: detail.HotelStaticProfile,
			})
		}
	}
	return &domain.BatchHotelStaticDetailResp{
		Hotels: hotels,
	}, nil
}

// 获取酒店列表
func (s *GiataClient) HotelList(ctx context.Context, req *domain.HotelListReq) (*domain.HotelListResp, error) {
	var supplierHotelIds []string
	if len(req.SupplierHotelIds) == 0 {
		// TODO: GIATA酒店ID列表获取逻辑，需根据实际API实现
		return nil, nil
	} else {
		supplierHotelIds = req.SupplierHotelIds
	}
	hotelDetails, err := s.batchHotelStaticDetail(ctx, supplierHotelIds)
	if err != nil {
		return nil, err
	}
	return &domain.HotelListResp{
		Hotels: hotelDetails.Hotels,
	}, nil
}

// HotelIdList 获取酒店ID列表（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) HotelIdList(ctx context.Context, req *domain.HotelIdListReq) (*domain.HotelIdListResp, error) {
	return nil, errors.New("HotelIdList not implemented for GIATA")
}

// HotelRates 获取酒店房型和价格（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) HotelRates(ctx context.Context, req *domain.HotelRatesReq) (*domain.HotelRatesResp, error) {
	return nil, errors.New("HotelRates not implemented for GIATA")
}

// CheckAvail 查询是否可以下定（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) CheckAvail(ctx context.Context, req *domain.CheckAvailReq) (*domain.CheckAvailResp, error) {
	return nil, errors.New("CheckAvail not implemented for GIATA")
}

// Book 下单（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) Book(ctx context.Context, req *domain.BookReq) (*domain.BookResp, error) {
	return nil, errors.New("Book not implemented for GIATA")
}

// QueryOrderByIDs 获取订单列表（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) QueryOrderByIDs(ctx context.Context, req *domain.QueryOrdersReq) (*domain.QueryOrdersResp, error) {
	return nil, errors.New("QueryOrderByIDs not implemented for GIATA")
}

// Cancel 取消订单（GIATA暂不支持，需补充API文档后实现）
func (s *GiataClient) Cancel(ctx context.Context, req *domain.CancelReq) (*domain.CancelResp, error) {
	return nil, errors.New("Cancel not implemented for GIATA")
}
