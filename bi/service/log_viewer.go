package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"hotel/bi/domain"
	"hotel/bi/mysql"
	"hotel/common/bizerr"
	"hotel/common/log"
)

func (s *LogService) GetLog(ctx context.Context, logID string) (*domain.HBLog, error) {
	res, err := s.QueryLogs(ctx, &domain.LogQuery{LogId: logID})
	if err != nil {
		return nil, err
	}
	if len(res.Logs) == 0 {
		return nil, bizerr.NotFoundErr.WithMessagef("log %s not found", logID)
	}
	return res.Logs[0], nil
}

func (s *LogService) QueryLogs(ctx context.Context, query *domain.LogQuery) (res *domain.QueryResult, err error) {
	var builder strings.Builder
	var args []interface{}

	builder.WriteString("SELECT timestamp, log_id, session_id, user_id, seller_entity_id, buyer_entity_id, " +
		"api_in_path, api_in_biz_type, api_in_biz_id, api_out_supplier, api_out_path, api_out_biz_type, api_out_biz_id, cost_time, " +
		"biz_error_code, input_credential, input_header, input_body, input_body_size, " +
		"output_http_status_code, output_header, output_body, output_body_size, output_cost_time, output_internal_cost_time, " +
		"kvs FROM hb_log WHERE 1=1")

	// Dynamically build query
	if !query.StartTime.IsZero() {
		builder.WriteString(" AND timestamp >= ?")
		args = append(args, query.StartTime)
	}
	if !query.EndTime.IsZero() {
		builder.WriteString(" AND timestamp <= ?")
		args = append(args, query.EndTime)
	}
	if query.LogId != "" {
		builder.WriteString(" AND log_id = ?")
		args = append(args, query.LogId)
	}
	if query.SessionId != "" {
		builder.WriteString(" AND session_id = ?")
		args = append(args, query.SessionId)
	}
	if query.UserId > 0 {
		builder.WriteString(" AND user_id = ?")
		args = append(args, query.UserId)
	}
	if query.SellerEntityId > 0 {
		builder.WriteString(" AND seller_entity_id = ?")
		args = append(args, query.SellerEntityId)
	}
	if query.BuyerEntityId > 0 {
		builder.WriteString(" AND buyer_entity_id = ?")
		args = append(args, query.BuyerEntityId)
	}
	if query.SupplierBizId != "" {
		builder.WriteString(" AND (api_in_biz_id = ? OR api_out_biz_id = ?)")
		args = append(args, query.SupplierBizId, query.SupplierBizId)
	}
	if query.SupplierBizType > 0 {
		// Convert int to string for comparison
		bizTypeStr := strconv.Itoa(query.SupplierBizType)
		builder.WriteString(" AND (api_in_biz_type = ? OR api_out_biz_type = ?)")
		args = append(args, bizTypeStr, bizTypeStr)
	}
	if query.ApiInPath != "" {
		builder.WriteString(" AND api_in_path = ?")
		args = append(args, query.ApiInPath)
	}
	if query.ApiOutSupplier != "" {
		builder.WriteString(" AND api_out_supplier = ?")
		args = append(args, query.ApiOutSupplier)
	}
	if query.ApiOutPath != "" {
		builder.WriteString(" AND api_out_path = ?")
		args = append(args, query.ApiOutPath)
	}
	if query.BizErrorCode != "" {
		builder.WriteString(" AND biz_error_code = ?")
		args = append(args, query.BizErrorCode)
	}
	if query.InputCredentialKey != "" && query.InputCredentialValue != "" {
		// 使用 JSON_EXTRACT 函数查询 JSON 类型中的特定键值
		builder.WriteString(" AND JSON_UNQUOTE(JSON_EXTRACT(input_credential, ?)) = ?")
		args = append(args, "$."+query.InputCredentialKey, query.InputCredentialValue)
	}
	if query.InputHeaderKey != "" && query.InputHeaderValue != "" {
		// 使用 JSON_EXTRACT 函数查询 JSON 类型中的特定键值
		builder.WriteString(" AND JSON_UNQUOTE(JSON_EXTRACT(input_header, ?)) = ?")
		args = append(args, "$."+query.InputHeaderKey, query.InputHeaderValue)
	}
	if query.InputBodyKeyword != "" {
		builder.WriteString(" AND input_body LIKE ?")
		args = append(args, "%"+query.InputBodyKeyword+"%")
	}
	if query.OutputBodyKeyword != "" {
		builder.WriteString(" AND output_body LIKE ?")
		args = append(args, "%"+query.OutputBodyKeyword+"%")
	}
	if query.OutputHeaderKey != "" && query.OutputHeaderValue != "" {
		// 使用 JSON_EXTRACT 函数查询 JSON 类型中的特定键值
		builder.WriteString(" AND JSON_UNQUOTE(JSON_EXTRACT(output_header, ?)) = ?")
		args = append(args, "$."+query.OutputHeaderKey, query.OutputHeaderValue)
	}
	if query.OutputHttpStatusCode > 0 {
		builder.WriteString(" AND output_http_status_code = ?")
		args = append(args, query.OutputHttpStatusCode)
	}
	if query.CostTimeMax > 0 {
		builder.WriteString(" AND cost_time <= ?")
		args = append(args, int64(query.CostTimeMax/time.Millisecond))
	}
	if query.CostTimeMin > 0 {
		builder.WriteString(" AND cost_time >= ?")
		args = append(args, int64(query.CostTimeMin/time.Millisecond))
	}

	// Count total
	var total int64
	countBuilder := strings.Builder{}
	countBuilder.WriteString("SELECT count(*) FROM hb_log WHERE 1=1")
	countBuilder.WriteString(strings.SplitN(builder.String(), "WHERE 1=1", 2)[1])
	err = s.conn.QueryRowCtx(ctx, &total, countBuilder.String(), args...)
	if err != nil {
		return nil, fmt.Errorf("failed to count logs: %w", err)
	}

	// Add order and pagination
	builder.WriteString(" ORDER BY timestamp DESC")
	if query.Page > 0 && query.PageSize > 0 {
		builder.WriteString(" LIMIT ? OFFSET ?")
		args = append(args, query.PageSize, (query.Page-1)*query.PageSize)
	}

	var dbLogs []*mysql.HBLogModel
	err = s.conn.QueryRowsCtx(ctx, &dbLogs, builder.String(), args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query logs: %w", err)
	}

	// Convert db models to domain models
	logs := make([]*domain.HBLog, len(dbLogs))
	for i, dbLog := range dbLogs {
		// 处理 MAP 类型字段的反序列化
		var inputHeader map[string]string
		var inputCredential map[string]string
		var outputHeader map[string]string
		var kvs map[string]interface{}

		// 反序列化 MAP 类型字段
		if err := json.Unmarshal([]byte(dbLog.InputHeader), &inputHeader); err != nil {
			log.Errorc(ctx, "failed to unmarshal input_header", "log_id", dbLog.LogId, "error", err)
			inputHeader = make(map[string]string)
		}

		if err := json.Unmarshal([]byte(dbLog.InputCredential), &inputCredential); err != nil {
			log.Errorc(ctx, "failed to unmarshal input_credential", "log_id", dbLog.LogId, "error", err)
			inputCredential = make(map[string]string)
		}

		if err := json.Unmarshal([]byte(dbLog.OutputHeader), &outputHeader); err != nil {
			log.Errorc(ctx, "failed to unmarshal output_header", "log_id", dbLog.LogId, "error", err)
			outputHeader = make(map[string]string)
		}

		if err := json.Unmarshal([]byte(dbLog.Kvs), &kvs); err != nil {
			log.Errorc(ctx, "failed to unmarshal kvs", "log_id", dbLog.LogId, "error", err)
			kvs = make(map[string]interface{})
		}

		// 构建 domain.HBLog 对象
		logs[i] = &domain.HBLog{
			Timestamp:      dbLog.Timestamp,
			KVs:            kvs,
			UserId:         dbLog.UserId,
			LogId:          dbLog.LogId,
			SessionId:      dbLog.SessionId,
			SellerEntityId: dbLog.SellerEntityId,
			BuyerEntityId:  dbLog.BuyerEntityId,
			APIIn: &domain.APIIn{
				Path:    dbLog.ApiInPath,
				BizType: dbLog.ApiInBizType,
				BizId:   dbLog.ApiInBizId,
			},
			APIOut: &domain.APIOut{
				Path:     dbLog.ApiOutPath,
				Supplier: dbLog.ApiOutSupplier,
			},
			Input: &domain.Input{
				Header:     inputHeader,
				Body:       dbLog.InputBody,
				Credential: inputCredential,
			},
			Output: &domain.Output{
				Header:         outputHeader,
				Body:           dbLog.OutputBody,
				CostTime:       time.Duration(dbLog.OutputCostTime) * time.Millisecond,
				HttpStatusCode: dbLog.OutputHttpStatusCode,
			},
			// 从 BizErrorCode 构建 BizError - 需要转换为int32
			BizError: func() *bizerr.BizError {
				if dbLog.BizErrorCode == "" {
					return bizerr.New(0, "")
				}
				// 尝试解析为int32，如果失败则使用0
				if code, err := strconv.ParseInt(dbLog.BizErrorCode, 10, 32); err == nil {
					return bizerr.New(int32(code), "")
				}
				return bizerr.New(0, "")
			}(),
			CostTime: time.Duration(dbLog.CostTime) * time.Millisecond,
		}
	}

	return &domain.QueryResult{Logs: logs, Total: total}, nil
}
