package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Danceiny/sentinel-golang/core/flow"
	"github.com/bytedance/arishem/typedef"

	"hotel/common/bizerr"
	commonDomain "hotel/common/domain"
	"hotel/rule/domain"
	"hotel/rule/mysql"
	userDomain "hotel/user/domain"
)

// QuotaRuleService 配额规则服务
type QuotaRuleService struct {
	ruleDao *mysql.Dao
}

func NewQuotaRuleService(ruleDao *mysql.Dao) *QuotaRuleService {
	return &QuotaRuleService{
		ruleDao: ruleDao,
	}
}

// QuotaConfig 配额配置
type QuotaConfig struct {
	QuotaType   string  `json:"quota_type"`  // 配额类型：api_request, search_query, booking_create, otp_send, user_invite
	LimitValue  int64   `json:"limit_value"` // 限制值
	TimeWindow  string  `json:"time_window"` // 时间窗口：minute, hour, day, month
	Threshold   float64 `json:"threshold"`   // 阈值（0.0-1.0）
	Enabled     bool    `json:"enabled"`     // 是否启用
	Description string  `json:"description"` // 描述
}

// QuotaRuleParams 配额规则参数
type QuotaRuleParams struct {
	EntityID    int64                 `json:"entity_id"`
	EntityType  userDomain.EntityType `json:"entity_type"`
	QuotaType   string                `json:"quota_type"`
	RequestPath string                `json:"request_path"`
	CurrentTime time.Time             `json:"current_time"`
	UserID      int64                 `json:"user_id"`
	TenantID    int64                 `json:"tenant_id"`
	CustomerID  int64                 `json:"customer_id"`
}

// 配额类型常量
const (
	QuotaTypeAPIRequest    = "api_request"
	QuotaTypeSearchQuery   = "search_query"
	QuotaTypeBookingCreate = "booking_create"
	QuotaTypeOTPSend       = "otp_send"
	QuotaTypeUserInvite    = "user_invite"
)

// 时间窗口常量
const (
	TimeWindowMinute = "minute"
	TimeWindowHour   = "hour"
	TimeWindowDay    = "day"
	TimeWindowMonth  = "month"
)

// CreateQuotaRule 创建配额规则
func (s *QuotaRuleService) CreateQuotaRule(ctx context.Context, entityID int64, entityType userDomain.EntityType, config *QuotaConfig) error {
	// 构建规则名称
	ruleName := fmt.Sprintf("quota_%s_%d_%s_%s", entityType.String(), entityID, config.QuotaType, config.TimeWindow)

	// 构建条件表达式 - 检查配额类型和实体匹配
	condition := fmt.Sprintf(`{
		"OpLogic": "&&",
		"Conditions": [
			{
				"Operator": "==",
				"Lhs": {"VarExpr": "entity_id"},
				"Rhs": {"Const": {"NumConst": %d}}
			},
			{
				"Operator": "==",
				"Lhs": {"VarExpr": "entity_type"},
				"Rhs": {"Const": {"NumConst": %d}}
			},
			{
				"Operator": "==",
				"Lhs": {"VarExpr": "quota_type"},
				"Rhs": {"Const": {"StrConst": "%s"}}
			},
			{
				"Operator": "==",
				"Lhs": {"VarExpr": "enabled"},
				"Rhs": {"Const": {"BoolConst": %t}}
			}
		]
	}`, entityID, int(entityType), config.QuotaType, config.Enabled)

	// 构建输出表达式 - 返回配额配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配额配置失败: %v", err)
	}

	aim := fmt.Sprintf(`{
		"ActionName": "QUOTA_CONFIG",
		"ParamMap": {
			"config": %s
		}
	}`, string(configJSON))

	// 创建规则
	rule := &domain.Rule{
		EntityId:  entityID,
		Name:      ruleName,
		Condition: condition,
		Aim:       aim,
		Status:    commonDomain.StatusEnable,
	}

	_, err = s.ruleDao.CreateRule(ctx, rule)
	if err != nil {
		return fmt.Errorf("创建配额规则失败: %v", err)
	}

	// 同步更新 Sentinel 流控规则
	return s.updateSentinelRules(ctx, entityID, entityType, config)
}

// GetQuotaConfig 获取配额配置
func (s *QuotaRuleService) GetQuotaConfig(ctx context.Context, params *QuotaRuleParams) (*QuotaConfig, error) {
	// 构建规则名称
	ruleName := fmt.Sprintf("quota_%s_%d_%s", params.EntityType.String(), params.EntityID, params.QuotaType)

	// 查找规则
	rule, err := s.ruleDao.GetRuleByName(ctx, ruleName+"_"+TimeWindowHour) // 默认查找小时级别
	if err != nil {
		if bizerr.NotFoundErr.Is(err) {
			// 如果没有找到规则，返回默认配置
			return s.getDefaultQuotaConfig(params.QuotaType), nil
		}
		return nil, err
	}

	// 执行规则获取配额配置
	ruleParams := typedef.MetaType{
		"entity_id":    params.EntityID,
		"entity_type":  int(params.EntityType),
		"quota_type":   params.QuotaType,
		"enabled":      true,
		"current_time": params.CurrentTime,
		"user_id":      params.UserID,
		"tenant_id":    params.TenantID,
		"customer_id":  params.CustomerID,
	}

	result, err := rule.RunWithMap(ctx, ruleParams)
	if err != nil {
		return nil, fmt.Errorf("执行配额规则失败: %v", err)
	}

	// 解析结果
	if result == nil {
		return s.getDefaultQuotaConfig(params.QuotaType), nil
	}

	// 从结果中提取配额配置
	if config := s.parseQuotaConfigFromResult(result); config != nil {
		return config, nil
	}

	return s.getDefaultQuotaConfig(params.QuotaType), nil
}

// UpdateQuotaRule 更新配额规则
func (s *QuotaRuleService) UpdateQuotaRule(ctx context.Context, entityID int64, entityType userDomain.EntityType, config *QuotaConfig) error {
	// 构建规则名称
	ruleName := fmt.Sprintf("quota_%s_%d_%s_%s", entityType.String(), entityID, config.QuotaType, config.TimeWindow)

	// 查找现有规则
	existingRule, err := s.ruleDao.GetRuleByName(ctx, ruleName)
	if err != nil {
		if bizerr.NotFoundErr.Is(err) {
			// 如果规则不存在，创建新规则
			return s.CreateQuotaRule(ctx, entityID, entityType, config)
		}
		return err
	}

	// 更新规则的输出表达式
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("序列化配额配置失败: %v", err)
	}

	aim := fmt.Sprintf(`{
		"ActionName": "QUOTA_CONFIG",
		"ParamMap": {
			"config": %s
		}
	}`, string(configJSON))

	existingRule.Aim = aim
	existingRule.Status = commonDomain.Status(map[bool]int{true: int(commonDomain.StatusEnable), false: int(commonDomain.StatusDisable)}[config.Enabled])

	err = s.ruleDao.UpdateRule(ctx, existingRule)
	if err != nil {
		return fmt.Errorf("更新配额规则失败: %v", err)
	}

	// 同步更新 Sentinel 流控规则
	return s.updateSentinelRules(ctx, entityID, entityType, config)
}

// DeleteQuotaRule 删除配额规则
func (s *QuotaRuleService) DeleteQuotaRule(ctx context.Context, entityID int64, entityType userDomain.EntityType, quotaType, timeWindow string) error {
	// 构建规则名称
	ruleName := fmt.Sprintf("quota_%s_%d_%s_%s", entityType.String(), entityID, quotaType, timeWindow)

	// 查找规则
	rule, err := s.ruleDao.GetRuleByName(ctx, ruleName)
	if err != nil {
		return err
	}

	// 删除规则
	err = s.ruleDao.DeleteRule(ctx, rule.ID)
	if err != nil {
		return fmt.Errorf("删除配额规则失败: %v", err)
	}

	// 移除 Sentinel 流控规则
	return s.removeSentinelRules(ctx, entityID, entityType, quotaType)
}

// updateSentinelRules 更新 Sentinel 流控规则
func (s *QuotaRuleService) updateSentinelRules(ctx context.Context, entityID int64, entityType userDomain.EntityType, config *QuotaConfig) error {
	if !config.Enabled {
		return s.removeSentinelRules(ctx, entityID, entityType, config.QuotaType)
	}

	// 构建资源名称
	resourceName := fmt.Sprintf("%sEntity:%d@%s", entityType.String(), entityID, config.QuotaType)

	// 计算时间窗口（秒）
	intervalInSec := s.getIntervalInSeconds(config.TimeWindow)

	// 创建 Sentinel 流控规则
	flowRule := &flow.Rule{
		Resource:               resourceName,
		TokenCalculateStrategy: flow.Direct,
		ControlBehavior:        flow.Reject,
		Threshold:              float64(config.LimitValue),
		StatIntervalInMs:       uint32(intervalInSec * 1000),
	}

	// 加载规则到 Sentinel
	_, err := flow.LoadRules([]*flow.Rule{flowRule})
	if err != nil {
		return fmt.Errorf("加载 Sentinel 流控规则失败: %v", err)
	}

	return nil
}

// removeSentinelRules 移除 Sentinel 流控规则
func (s *QuotaRuleService) removeSentinelRules(ctx context.Context, entityID int64, entityType userDomain.EntityType, quotaType string) error {
	// 获取当前所有规则
	currentRules := flow.GetRules()

	// 过滤掉要删除的规则
	resourceName := fmt.Sprintf("%sEntity:%d@%s", entityType.String(), entityID, quotaType)
	filteredRules := make([]*flow.Rule, 0)
	for _, rule := range currentRules {
		if rule.Resource != resourceName {
			filteredRules = append(filteredRules, &rule)
		}
	}

	// 重新加载过滤后的规则
	_, err := flow.LoadRules(filteredRules)
	if err != nil {
		return fmt.Errorf("移除 Sentinel 流控规则失败: %v", err)
	}

	return nil
}

// getDefaultQuotaConfig 获取默认配额配置
func (s *QuotaRuleService) getDefaultQuotaConfig(quotaType string) *QuotaConfig {
	defaultConfigs := map[string]*QuotaConfig{
		QuotaTypeAPIRequest: {
			QuotaType:   QuotaTypeAPIRequest,
			LimitValue:  1000,
			TimeWindow:  TimeWindowHour,
			Threshold:   0.8,
			Enabled:     true,
			Description: "API 请求默认配额",
		},
		QuotaTypeSearchQuery: {
			QuotaType:   QuotaTypeSearchQuery,
			LimitValue:  500,
			TimeWindow:  TimeWindowHour,
			Threshold:   0.8,
			Enabled:     true,
			Description: "搜索查询默认配额",
		},
		QuotaTypeBookingCreate: {
			QuotaType:   QuotaTypeBookingCreate,
			LimitValue:  100,
			TimeWindow:  TimeWindowHour,
			Threshold:   0.9,
			Enabled:     true,
			Description: "创建预订默认配额",
		},
		QuotaTypeOTPSend: {
			QuotaType:   QuotaTypeOTPSend,
			LimitValue:  5,
			TimeWindow:  TimeWindowHour,
			Threshold:   1.0,
			Enabled:     true,
			Description: "OTP 发送默认配额",
		},
		QuotaTypeUserInvite: {
			QuotaType:   QuotaTypeUserInvite,
			LimitValue:  10,
			TimeWindow:  TimeWindowDay,
			Threshold:   0.8,
			Enabled:     true,
			Description: "用户邀请默认配额",
		},
	}

	if config, exists := defaultConfigs[quotaType]; exists {
		return config
	}

	// 返回通用默认配置
	return &QuotaConfig{
		QuotaType:   quotaType,
		LimitValue:  100,
		TimeWindow:  TimeWindowHour,
		Threshold:   0.8,
		Enabled:     true,
		Description: "通用默认配额",
	}
}

// getIntervalInSeconds 获取时间窗口对应的秒数
func (s *QuotaRuleService) getIntervalInSeconds(timeWindow string) int64 {
	switch timeWindow {
	case TimeWindowMinute:
		return 60
	case TimeWindowHour:
		return 3600
	case TimeWindowDay:
		return 86400
	case TimeWindowMonth:
		return 2592000 // 30天
	default:
		return 3600 // 默认1小时
	}
}

// ListQuotaRules 列出实体的所有配额规则
func (s *QuotaRuleService) ListQuotaRules(ctx context.Context, entityID int64, entityType userDomain.EntityType) ([]*QuotaConfig, error) {
	// 获取所有规则
	allRules, err := s.ruleDao.GetAllRules(ctx)
	if err != nil {
		return nil, err
	}

	// 过滤出配额规则
	quotaConfigs := make([]*QuotaConfig, 0)
	for _, rule := range allRules {
		if rule.EntityId == entityID && rule.Status == commonDomain.StatusEnable {
			// 检查是否是配额规则
			if len(rule.Name) > 6 && rule.Name[:6] == "quota_" {
				// 执行规则获取配额配置
				ruleParams := typedef.MetaType{
					"entity_id":   entityID,
					"entity_type": int(entityType),
					"enabled":     true,
				}

				result, err := rule.RunWithMap(ctx, ruleParams)
				if err != nil {
					continue
				}

				// 解析配额配置
				if config := s.parseQuotaConfigFromResult(result); config != nil {
					quotaConfigs = append(quotaConfigs, config)
				}
			}
		}
	}

	return quotaConfigs, nil
}

// parseQuotaConfigFromResult 从规则结果中解析配额配置
func (s *QuotaRuleService) parseQuotaConfigFromResult(result interface{}) *QuotaConfig {
	if result == nil {
		return nil
	}

	if actionResult, ok := result.(map[string]interface{}); ok {
		if paramMap, ok := actionResult["ParamMap"].(map[string]interface{}); ok {
			if configData, ok := paramMap["config"]; ok {
				configJSON, _ := json.Marshal(configData)
				var config QuotaConfig
				if err := json.Unmarshal(configJSON, &config); err == nil {
					return &config
				}
			}
		}
	}

	return nil
}
