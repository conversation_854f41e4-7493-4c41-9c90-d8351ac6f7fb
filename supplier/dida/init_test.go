package dida

import (
	"context"

	common "hotel/common/domain"
	"hotel/supplier/dida/model"
	"hotel/supplier/domain"
)

const (
	TestAccount  = "DidaApiTestID"
	TestPassword = "TestKey"
)

const (
	TestAccount2  = "TravelToDiscover_Test"
	TestPassword2 = "vO8sRbn0aMYM93VnNviYFXb7B83Ri3iJ"
)

func testCtx() context.Context {
	return domain.InjectBaseRequestContextPayload(context.Background(), domain.BaseRequestContextPayload{
		Properties: model.Properties{
			Header: model.Header{
				ClientID:   TestAccount,
				LicenseKey: TestPassword,
			},
		},
		Session: &common.Session{
			Id:     "test-session-id",
			Params: make(map[string]string),
		},
	})
}
