package quota

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/Danceiny/sentinel-golang/core/flow"

	commonDomain "hotel/common/domain"
	"hotel/common/log"
	ruleDomain "hotel/rule/domain"
	ruleMySQL "hotel/rule/mysql"
	userDomain "hotel/user/domain"
)

// QuotaConfig 配额配置结构
type QuotaConfig struct {
	QuotaType    string `json:"quotaType"`    // 配额类型：api, request, data 等
	Limit        int64  `json:"limit"`        // 配额限制数量
	Interval     string `json:"interval"`     // 时间间隔：minute, hour, day, month
	ResourcePath string `json:"resourcePath"` // 资源路径，可选
}

// QuotaRuleService 配额规则服务
type QuotaRuleService struct {
	ruleDao *ruleMySQL.Dao
}

// NewQuotaRuleService 创建配额规则服务实例
func NewQuotaRuleService(ruleDao *ruleMySQL.Dao) *QuotaRuleService {
	return &QuotaRuleService{
		ruleDao: ruleDao,
	}
}

// CreateQuotaRule 创建配额规则
func (s *QuotaRuleService) CreateQuotaRule(ctx context.Context, entityID string, entityType userDomain.EntityType, config QuotaConfig) error {
	// 构建规则名称
	ruleName := fmt.Sprintf("quota_%s_%s_%s", entityType.String(), entityID, config.QuotaType)
	
	// 构建规则条件
	condition := map[string]interface{}{
		"entityID":     entityID,
		"entityType":   entityType.String(),
		"quotaType":    config.QuotaType,
		"limit":        config.Limit,
		"interval":     config.Interval,
		"resourcePath": config.ResourcePath,
	}
	
	conditionJSON, err := json.Marshal(condition)
	if err != nil {
		return fmt.Errorf("failed to marshal condition: %w", err)
	}
	
	// 创建规则
	entityIDInt, err := strconv.ParseInt(entityID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid entity ID: %w", err)
	}
	
	rule := &ruleDomain.Rule{
		EntityId:  entityIDInt,
		Name:      ruleName,
		Condition: string(conditionJSON),
		Aim:       "quota_management",
		Status:    commonDomain.StatusEnable,
	}
	
	_, err = s.ruleDao.CreateRule(ctx, rule)
	if err != nil {
		return fmt.Errorf("failed to create quota rule: %w", err)
	}
	
	// 同步到 Sentinel
	return s.updateSentinelRules(entityID, entityType, config)
}

// GetQuotaConfig 获取配额配置
func (s *QuotaRuleService) GetQuotaConfig(ctx context.Context, entityID string, entityType userDomain.EntityType, quotaType string) (*QuotaConfig, error) {
	ruleName := fmt.Sprintf("quota_%s_%s_%s", entityType.String(), entityID, quotaType)
	
	rule, err := s.ruleDao.GetRuleByName(ctx, ruleName)
	if err != nil {
		return nil, fmt.Errorf("failed to get quota rule: %w", err)
	}
	
	if rule == nil {
		// 返回默认配额配置
		return s.getDefaultQuotaConfig(quotaType), nil
	}
	
	// 执行规则获取配置
	inputJSON, err := json.Marshal(map[string]interface{}{
		"entityID":   entityID,
		"entityType": entityType.String(),
		"quotaType":  quotaType,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal input: %w", err)
	}
	
	result, err := rule.RunWithJSON(ctx, string(inputJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to execute quota rule: %w", err)
	}
	
	return s.parseQuotaConfigFromResult(result)
}

// UpdateQuotaRule 更新配额规则
func (s *QuotaRuleService) UpdateQuotaRule(ctx context.Context, entityID string, entityType userDomain.EntityType, config QuotaConfig) error {
	ruleName := fmt.Sprintf("quota_%s_%s_%s", entityType.String(), entityID, config.QuotaType)
	
	// 构建新的条件
	condition := map[string]interface{}{
		"entityID":     entityID,
		"entityType":   entityType.String(),
		"quotaType":    config.QuotaType,
		"limit":        config.Limit,
		"interval":     config.Interval,
		"resourcePath": config.ResourcePath,
	}
	
	conditionJSON, err := json.Marshal(condition)
	if err != nil {
		return fmt.Errorf("failed to marshal condition: %w", err)
	}
	
	// 更新规则
	entityIDInt, err := strconv.ParseInt(entityID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid entity ID: %w", err)
	}
	
	rule := &ruleDomain.Rule{
		EntityId:  entityIDInt,
		Name:      ruleName,
		Condition: string(conditionJSON),
		Aim:       "quota_management",
		Status:    commonDomain.StatusEnable,
	}
	
	err = s.ruleDao.UpsertRule(ctx, rule)
	if err != nil {
		return fmt.Errorf("failed to update quota rule: %w", err)
	}
	
	// 同步到 Sentinel
	return s.updateSentinelRules(entityID, entityType, config)
}

// DeleteQuotaRule 删除配额规则
func (s *QuotaRuleService) DeleteQuotaRule(ctx context.Context, entityID string, entityType userDomain.EntityType, quotaType string) error {
	ruleName := fmt.Sprintf("quota_%s_%s_%s", entityType.String(), entityID, quotaType)
	
	// 首先获取规则以获得ID
	rule, err := s.ruleDao.GetRuleByName(ctx, ruleName)
	if err != nil {
		return fmt.Errorf("failed to get quota rule for deletion: %w", err)
	}
	if rule == nil {
		return fmt.Errorf("quota rule not found: %s", ruleName)
	}
	
	err = s.ruleDao.DeleteRule(ctx, rule.ID)
	if err != nil {
		return fmt.Errorf("failed to delete quota rule: %w", err)
	}
	
	// 从 Sentinel 中移除规则
	return s.removeSentinelRules(entityID, entityType, quotaType)
}

// updateSentinelRules 更新 Sentinel 流控规则
func (s *QuotaRuleService) updateSentinelRules(entityID string, entityType userDomain.EntityType, config QuotaConfig) error {
	// 构建资源名称
	resourceNames := s.buildResourceNames(entityID, entityType, config)
	
	// 获取现有规则
	existingRules := flow.GetRules()
	
	// 过滤掉相关的旧规则
	filteredRules := make([]*flow.Rule, 0)
	for _, rule := range existingRules {
		isQuotaRule := false
		for _, resourceName := range resourceNames {
			if rule.Resource == resourceName {
				isQuotaRule = true
				break
			}
		}
		if !isQuotaRule {
			filteredRules = append(filteredRules, &rule)
		}
	}
	
	// 添加新规则
	intervalMs := s.getIntervalInSeconds(config.Interval) * 1000
	for _, resourceName := range resourceNames {
		newRule := &flow.Rule{
			Resource:          resourceName,
			TokenCalculateStrategy: flow.Direct,
			ControlBehavior:   flow.Reject,
			Threshold:         float64(config.Limit),
			StatIntervalInMs:  uint32(intervalMs),
		}
		filteredRules = append(filteredRules, newRule)
	}
	
	// 重新加载规则
	_, err := flow.LoadRules(filteredRules)
	if err != nil {
		return fmt.Errorf("failed to load sentinel rules: %w", err)
	}
	
	log.Info("Updated Sentinel quota rules for entity %s, type %s, quota type %s", entityID, entityType.String(), config.QuotaType)
	return nil
}

// removeSentinelRules 从 Sentinel 中移除规则
func (s *QuotaRuleService) removeSentinelRules(entityID string, entityType userDomain.EntityType, quotaType string) error {
	// 构建资源名称
	config := QuotaConfig{QuotaType: quotaType}
	resourceNames := s.buildResourceNames(entityID, entityType, config)
	
	// 获取现有规则
	existingRules := flow.GetRules()
	
	// 过滤掉要删除的规则
	filteredRules := make([]*flow.Rule, 0)
	for _, rule := range existingRules {
		isTargetRule := false
		for _, resourceName := range resourceNames {
			if rule.Resource == resourceName {
				isTargetRule = true
				break
			}
		}
		if !isTargetRule {
			filteredRules = append(filteredRules, &rule)
		}
	}
	
	// 重新加载规则
	_, err := flow.LoadRules(filteredRules)
	if err != nil {
		return fmt.Errorf("failed to reload sentinel rules: %w", err)
	}
	
	log.Info("Removed Sentinel quota rules for entity %s, type %s, quota type %s", entityID, entityType.String(), quotaType)
	return nil
}

// buildResourceNames 构建资源名称列表
func (s *QuotaRuleService) buildResourceNames(entityID string, entityType userDomain.EntityType, config QuotaConfig) []string {
	resourceNames := make([]string, 0)
	
	if config.ResourcePath != "" {
		// 特定路径的配额
		resourceNames = append(resourceNames, fmt.Sprintf("quota:%s@%s", entityID, config.ResourcePath))
	} else {
		// 通用配额
		resourceNames = append(resourceNames, fmt.Sprintf("quota:%s", entityID))
	}
	
	// 添加默认配额作为后备
	if entityID != "default" {
		resourceNames = append(resourceNames, "quota:default")
	}
	
	return resourceNames
}

// getDefaultQuotaConfig 获取默认配额配置
func (s *QuotaRuleService) getDefaultQuotaConfig(quotaType string) *QuotaConfig {
	switch quotaType {
	case "api":
		return &QuotaConfig{
			QuotaType: "api",
			Limit:     1000,
			Interval:  "hour",
		}
	case "request":
		return &QuotaConfig{
			QuotaType: "request",
			Limit:     100,
			Interval:  "minute",
		}
	case "data":
		return &QuotaConfig{
			QuotaType: "data",
			Limit:     10000,
			Interval:  "day",
		}
	default:
		return &QuotaConfig{
			QuotaType: quotaType,
			Limit:     100,
			Interval:  "hour",
		}
	}
}

// getIntervalInSeconds 将时间间隔转换为秒数
func (s *QuotaRuleService) getIntervalInSeconds(interval string) int64 {
	switch strings.ToLower(interval) {
	case "second":
		return 1
	case "minute":
		return 60
	case "hour":
		return 3600
	case "day":
		return 86400
	case "month":
		return 2592000 // 30 days
	default:
		return 3600 // 默认1小时
	}
}

// ListQuotaRules 列出指定实体的所有配额规则
func (s *QuotaRuleService) ListQuotaRules(ctx context.Context, entityID string, entityType userDomain.EntityType) ([]*QuotaConfig, error) {
	rules, err := s.ruleDao.GetAllRules(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get rules: %w", err)
	}
	
	entityIDInt, err := strconv.ParseInt(entityID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid entity ID: %w", err)
	}
	
	var quotaConfigs []*QuotaConfig
	for _, rule := range rules {
		if rule.EntityId == entityIDInt && rule.Aim == "quota_management" && rule.Status == commonDomain.StatusEnable {
			config, err := s.parseQuotaConfigFromCondition(rule.Condition)
			if err != nil {
				log.Warn("Failed to parse quota config from rule %s: %v", rule.Name, err)
				continue
			}
			quotaConfigs = append(quotaConfigs, config)
		}
	}
	
	return quotaConfigs, nil
}

// parseQuotaConfigFromResult 从规则执行结果解析配额配置
func (s *QuotaRuleService) parseQuotaConfigFromResult(result interface{}) (*QuotaConfig, error) {
	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result type: %T", result)
	}
	
	config := &QuotaConfig{}
	
	if quotaType, ok := resultMap["quotaType"].(string); ok {
		config.QuotaType = quotaType
	}
	
	if limit, ok := resultMap["limit"].(float64); ok {
		config.Limit = int64(limit)
	} else if limitStr, ok := resultMap["limit"].(string); ok {
		if parsedLimit, err := strconv.ParseInt(limitStr, 10, 64); err == nil {
			config.Limit = parsedLimit
		}
	}
	
	if interval, ok := resultMap["interval"].(string); ok {
		config.Interval = interval
	}
	
	if resourcePath, ok := resultMap["resourcePath"].(string); ok {
		config.ResourcePath = resourcePath
	}
	
	return config, nil
}

// parseQuotaConfigFromCondition 从规则条件解析配额配置
func (s *QuotaRuleService) parseQuotaConfigFromCondition(condition string) (*QuotaConfig, error) {
	var conditionMap map[string]interface{}
	err := json.Unmarshal([]byte(condition), &conditionMap)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal condition: %w", err)
	}
	
	config := &QuotaConfig{}
	
	if quotaType, ok := conditionMap["quotaType"].(string); ok {
		config.QuotaType = quotaType
	}
	
	if limit, ok := conditionMap["limit"].(float64); ok {
		config.Limit = int64(limit)
	}
	
	if interval, ok := conditionMap["interval"].(string); ok {
		config.Interval = interval
	}
	
	if resourcePath, ok := conditionMap["resourcePath"].(string); ok {
		config.ResourcePath = resourcePath
	}
	
	return config, nil
}