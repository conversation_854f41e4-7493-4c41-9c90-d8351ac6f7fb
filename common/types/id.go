package types

import (
	"encoding/json"
	"fmt"
	"strconv"

	"hotel/common/utils"
)

// ID 自定义ID类型，支持string和int64的JSON序列化/反序列化
type ID int64
type IDs []ID

func (ids IDs) Join(sep string) string {
	return utils.JoinInt64Slice(ids.Int64s(), sep)
}

func (ids IDs) Int64s() (out []int64) {
	for _, id := range ids {
		out = append(out, id.Int64())
	}
	return out
}

// UnmarshalJSON 实现JSON反序列化，支持string和number类型
func (id *ID) UnmarshalJSON(data []byte) error {
	// 去除引号，检查是否为字符串
	if len(data) >= 2 && data[0] == '"' && data[len(data)-1] == '"' {
		// 字符串类型，去除引号
		str := string(data[1 : len(data)-1])
		if str == "" {
			*id = 0
			return nil
		}

		val, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			return fmt.Errorf("invalid ID string: %s", str)
		}
		*id = ID(val)
		return nil
	}

	// 数字类型，直接解析
	var val int64
	if err := json.Unmarshal(data, &val); err != nil {
		return fmt.Errorf("invalid ID number: %s", string(data))
	}
	*id = ID(val)
	return nil
}

// MarshalJSON 实现JSON序列化，输出为字符串以避免JavaScript大数问题
func (id ID) MarshalJSON() ([]byte, error) {
	return json.Marshal(strconv.FormatInt(int64(id), 10))
}

// String 返回字符串表示
func (id ID) String() string {
	return strconv.FormatInt(int64(id), 10)
}

// Int64 返回int64值
func (id ID) Int64() int64 {
	return int64(id)
}
func (id ID) Uint64() uint64 {
	return uint64(id)
}

// IsZero 检查是否为零值
func (id ID) IsZero() bool {
	return id == 0
}

// Valid 检查是否为有效ID（大于0）
func (id ID) Valid() bool {
	return id > 0
}
