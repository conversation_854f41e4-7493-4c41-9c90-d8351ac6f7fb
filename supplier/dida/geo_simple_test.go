package dida

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"hotel/supplier/dida/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestDidaClient_GetAllCountryInfo_Simple 简化版本的地理数据下载测试
// 使用测试认证信息直接调用API
// This test only runs when ENV=DEV to avoid modifying config files in other environments
func TestDidaClient_GetAllCountryInfo_Simple(t *testing.T) {
	// Skip this test unless ENV=DEV
	if os.Getenv("ENV") != "DEV" {
		t.Skip("Skipping TestDidaClient_GetAllCountryInfo_Simple: only runs when ENV=DEV")
		return
	}

	// 使用测试上下文，包含认证信息
	ctx := testCtx()

	client := NewDidaClient()
	require.NotNil(t, client)

	// 创建输出目录
	outputDir := "../../geography/config/dida"
	err := os.MkdirAll(outputDir, 0755)
	require.NoError(t, err)

	// 测试语言列表
	languages := []string{"en-US", "zh-CN"}

	for _, language := range languages {
		t.Run(fmt.Sprintf("Language_%s", language), func(t *testing.T) {
			t.Logf("Testing countries API for language: %s", language)

			// 先测试获取国家列表
			countries, err := client.GetCountries(ctx, language)
			if err != nil {
				t.Logf("Failed to get countries for language %s: %v", language, err)
				t.Skip("Skipping due to API error - this might be expected in test environment")
				return
			}

			require.NotNil(t, countries)
			assert.NotEmpty(t, countries.TraceID)
			assert.NotEmpty(t, countries.Data)

			t.Logf("Successfully retrieved %d countries for language %s", len(countries.Data), language)

			// 保存国家数据
			countriesFilename := fmt.Sprintf("countries_%s.json", language)
			countriesFilePath := filepath.Join(outputDir, countriesFilename)

			countriesFile, err := os.Create(countriesFilePath)
			require.NoError(t, err)
			defer countriesFile.Close()

			countriesEncoder := json.NewEncoder(countriesFile)
			countriesEncoder.SetIndent("", "  ")
			err = countriesEncoder.Encode(map[string]interface{}{
				"fetchedAt": time.Now().Format(time.RFC3339),
				"language":  language,
				"total":     len(countries.Data),
				"traceId":   countries.TraceID,
				"countries": countries.Data,
			})
			require.NoError(t, err)

			t.Logf("Saved countries data to: %s", countriesFilePath)

			// 测试几个主要国家的目的地
			testCountries := []string{"CN", "US", "GB", "JP", "FR"}

			allDestinations := make(map[string][]model.GeoDestination)

			for _, countryCode := range testCountries {
				// 检查国家是否存在
				var countryExists bool
				var countryName string
				for _, country := range countries.Data {
					if country.Code == countryCode {
						countryExists = true
						countryName = country.Name
						break
					}
				}

				if !countryExists {
					t.Logf("Country %s not found in countries list, skipping", countryCode)
					continue
				}

				t.Logf("Testing destinations for country: %s (%s)", countryName, countryCode)

				destinations, err := client.GetDestinations(ctx, countryCode, language)
				if err != nil {
					t.Logf("Failed to get destinations for country %s: %v", countryCode, err)
					continue
				}

				if destinations != nil && len(destinations.Data) > 0 {
					allDestinations[countryCode] = destinations.Data
					t.Logf("Retrieved %d destinations for %s (%s)", len(destinations.Data), countryName, countryCode)

					// 保存目的地数据
					destFilename := fmt.Sprintf("destinations_%s_%s_%s.json", countryCode, cleanFilename(countryName), language)
					destFilePath := filepath.Join(outputDir, destFilename)

					destFile, err := os.Create(destFilePath)
					if err != nil {
						t.Logf("Warning: Failed to create file %s: %v", destFilePath, err)
						continue
					}

					destEncoder := json.NewEncoder(destFile)
					destEncoder.SetIndent("", "  ")
					err = destEncoder.Encode(map[string]interface{}{
						"country": map[string]string{
							"code": countryCode,
							"name": countryName,
						},
						"destinations": destinations.Data,
						"fetchedAt":    time.Now().Format(time.RFC3339),
						"language":     language,
						"total":        len(destinations.Data),
						"traceId":      destinations.TraceID,
					})
					destFile.Close()

					if err != nil {
						t.Logf("Warning: Failed to encode destinations for %s: %v", countryName, err)
						continue
					}

					t.Logf("Saved destinations for %s to: %s", countryName, destFilename)
				} else {
					t.Logf("No destinations found for %s (%s)", countryName, countryCode)
				}

				// 添加延迟避免频率限制
				time.Sleep(200 * time.Millisecond)
			}

			// 保存汇总数据
			summaryFilename := fmt.Sprintf("geography_summary_%s.json", language)
			summaryFilePath := filepath.Join(outputDir, summaryFilename)

			summaryFile, err := os.Create(summaryFilePath)
			require.NoError(t, err)
			defer summaryFile.Close()

			totalDestinations := 0
			for _, dests := range allDestinations {
				totalDestinations += len(dests)
			}

			summaryEncoder := json.NewEncoder(summaryFile)
			summaryEncoder.SetIndent("", "  ")
			err = summaryEncoder.Encode(map[string]interface{}{
				"fetchedAt":                 time.Now().Format(time.RFC3339),
				"language":                  language,
				"totalCountries":            len(countries.Data),
				"testedCountries":           len(allDestinations),
				"totalDestinations":         totalDestinations,
				"countriesWithDestinations": allDestinations,
				"summary": map[string]interface{}{
					"countriesAPI":    "✓ Success",
					"destinationsAPI": fmt.Sprintf("✓ Tested %d countries", len(allDestinations)),
					"dataFiles":       fmt.Sprintf("Generated %d files", len(allDestinations)+2), // +2 for countries and summary
				},
			})
			require.NoError(t, err)

			t.Logf("Saved summary to: %s", summaryFilePath)
			t.Logf("Summary: %d countries, %d tested countries with destinations, %d total destinations",
				len(countries.Data), len(allDestinations), totalDestinations)
		})
	}
}

// TestDidaClient_GetCountries_Simple 简单的国家列表测试
func TestDidaClient_GetCountries_Simple(t *testing.T) {
	ctx := testCtx()
	client := NewDidaClient()

	// 测试默认语言
	resp, err := client.GetCountries(ctx, "")
	if err != nil {
		t.Logf("API call failed: %v", err)
		t.Skip("Skipping due to API error - this might be expected in test environment")
		return
	}

	require.NotNil(t, resp)
	assert.NotEmpty(t, resp.TraceID)
	assert.NotEmpty(t, resp.Timestamp)
	assert.NotEmpty(t, resp.Data)

	t.Logf("Retrieved %d countries", len(resp.Data))

	// 显示前几个国家
	for i, country := range resp.Data {
		if i >= 5 {
			break
		}
		t.Logf("Country %d: %s (%s)", i+1, country.Name, country.Code)
	}
}

// TestDidaClient_GetDestinations_Simple 简单的目的地测试
func TestDidaClient_GetDestinations_Simple(t *testing.T) {
	ctx := testCtx()
	client := NewDidaClient()

	// 测试中国的目的地
	resp, err := client.GetDestinations(ctx, "CN", "en-US")
	if err != nil {
		t.Logf("API call failed: %v", err)
		t.Skip("Skipping due to API error - this might be expected in test environment")
		return
	}

	require.NotNil(t, resp)
	assert.NotEmpty(t, resp.TraceID)
	assert.NotEmpty(t, resp.Timestamp)
	assert.NotEmpty(t, resp.Data)

	t.Logf("Retrieved %d destinations for China", len(resp.Data))

	// 显示前几个目的地
	for i, dest := range resp.Data {
		if i >= 5 {
			break
		}
		assert.Equal(t, "CN", dest.CountryCode)
		t.Logf("Destination %d: %s (%s)", i+1, dest.Name, dest.Code)
	}
}

// cleanFilename cleans filename for filesystem compatibility
func cleanFilename(filename string) string {
	// Replace problematic characters
	replacements := map[string]string{
			"/":  "_",
			"\\": "_",
			":":  "_",
			"*":  "_",
			"?":  "_",
			"\"": "_",
			"<":  "_",
			">":  "_",
			"|":  "_",
			" ": "_",
		}

	result := filename
	for old, new := range replacements {
		result = filepath.Base(result) // Ensure we only have the filename
		for i := 0; i < len(result); i++ {
			if string(result[i]) == old {
				result = result[:i] + new + result[i+1:]
			}
		}
	}

	return result
}
