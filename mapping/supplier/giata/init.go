package giata

import (
	"sync"

	configutils "hotel/common/config"
	"hotel/mapping/supplier/giata/model"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"
)

var (
	defaultConfigPath = "mapping/supplier/giata/config.yaml"
	configFile        = configutils.SafeFlagString("giata", defaultConfigPath, "the giata config file")

	_once        sync.Once
	_giataClient *GiataClient
)

func NewGiataClient() *GiataClient {
	_once.Do(func() {
		su := middleware.SupplierUtilWrapperFromConfigFilePath[model.Properties](domain.Supplier_Giata, *configFile)
		_giataClient = &GiataClient{
			SupplierUtilWrapper: su,
		}
	})
	return _giataClient
}
