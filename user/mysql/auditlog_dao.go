package mysql

import (
	"context"
	pkgerr "github.com/pkg/errors"
	"hotel/common/pagehelper"
	"time"

	"hotel/user/domain"
)

type AuditLogDao struct {
	auditLogModel *AuditLogModel
}

func (m *AuditLogDao) QueryByActionTypeAndTime(ctx context.Context, actionType string, actionTimeStart, actionTimeEnd time.Time, pageReq pagehelper.PageReq) ([]*domain.AuditLog, error) {
	t, err := m.auditLogModel.QueryByActionTypeAndTime(ctx, actionType, actionTimeStart, actionTimeEnd, pageReq)
	if err != nil {
		return nil, pkgerr.Wrap(err, "QueryByActionTypeAndTime")
	}
	var resp []*domain.AuditLog
	for _, in := range t {
		resp = append(resp, convert2DomainAuditLog(in))
	}
	return resp, nil
}
func (m *AuditLogDao) Insert(ctx context.Context, in *domain.AuditLog) error {
	v := convert2ModelAuditLog(in)
	_, err := m.auditLogModel.Insert(ctx, v)
	return err
}

func newAuditLogDao(auditLogModel *AuditLogModel) *AuditLogDao {
	return &AuditLogDao{
		auditLogModel: auditLogModel,
	}
}
