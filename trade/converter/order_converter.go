package converter

import (
	"hotel/common/types"
	"hotel/trade/dao"
	"hotel/trade/domain"
	"github.com/spf13/cast"
)

// DaoOrderToDomain 将 DAO 层的 Order 转换为 Domain 层的 Order
func DaoOrderToDomain(daoOrder *dao.Order) *domain.Order {
	if daoOrder == nil {
		return nil
	}

	return &domain.Order{
		Id:          types.ID(cast.ToInt64(daoOrder.Id)),
		ReferenceNo: daoOrder.ReferenceNo,
		Status:      domain.OrderStatus(daoOrder.Status),
		CreateTime:  daoOrder.CreateTime,
		// 其他字段可以根据需要添加
	}
}

// DaoOrderToDomainSummary 将 DAO 层的 Order 转换为 Domain 层的 OrderSummary
func DaoOrderToDomainSummary(daoOrder *dao.Order) *domain.OrderSummary {
	if daoOrder == nil {
		return nil
	}

	return &domain.OrderSummary{
		Id:          types.ID(cast.ToInt64(daoOrder.Id)),
		ReferenceNo: daoOrder.ReferenceNo,
		Status:      domain.OrderStatus(daoOrder.Status),
		CreateTime:  daoOrder.CreateTime,
		// 其他字段可以根据需要添加
		// Nights:      calculateNights(daoOrder.CheckIn, daoOrder.CheckOut),
		// Rooms:       1, // 默认值，可以根据实际业务逻辑调整
		// Tags:        []string{}, // 默认空数组
	}
}

// DaoOrderListToDomainList 将 DAO 层的 Order 列表转换为 Domain 层的 Order 列表
func DaoOrderListToDomainList(daoOrders []*dao.Order) []domain.Order {
	if len(daoOrders) == 0 {
		return []domain.Order{}
	}

	orders := make([]domain.Order, 0, len(daoOrders))
	for _, daoOrder := range daoOrders {
		if domainOrder := DaoOrderToDomain(daoOrder); domainOrder != nil {
			orders = append(orders, *domainOrder)
		}
	}
	return orders
}

// DaoOrderListToDomainSummaryList 将 DAO 层的 Order 列表转换为 Domain 层的 OrderSummary 列表
func DaoOrderListToDomainSummaryList(daoOrders []*dao.Order) []domain.OrderSummary {
	if len(daoOrders) == 0 {
		return []domain.OrderSummary{}
	}

	summaries := make([]domain.OrderSummary, 0, len(daoOrders))
	for _, daoOrder := range daoOrders {
		if summary := DaoOrderToDomainSummary(daoOrder); summary != nil {
			summaries = append(summaries, *summary)
		}
	}
	return summaries
}

// DaoSupplierOrderToDomain 将 DAO 层的 SupplierOrder 转换为 Domain 层的 SupplierOrder
func DaoSupplierOrderToDomain(daoSupplierOrder *dao.SupplierOrder) *domain.SupplierOrder {
	if daoSupplierOrder == nil {
		return nil
	}

	// 这里需要根据实际的 SupplierOrder 结构进行转换
	// 目前 domain.SupplierOrder 是空结构体，所以返回空对象
	return &domain.SupplierOrder{}
}

// calculateNights 计算住宿天数（辅助函数）
func calculateNights(checkIn, checkOut int64) int64 {
	if checkIn == 0 || checkOut == 0 || checkOut <= checkIn {
		return 0
	}
	// 这里可以实现具体的日期计算逻辑
	// 简单示例：假设是YYYYMMDD格式
	return 1 // 默认返回1晚，实际应该根据日期计算
}
