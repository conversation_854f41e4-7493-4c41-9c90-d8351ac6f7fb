package utils

import (
	"bufio"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"slices"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

func CloneStringMap(d map[string]string) map[string]string {
	res := make(map[string]string)
	for k, v := range d {
		res[k] = v
	}
	return res
}

// ReadLinesFunc reads all lines of the file.
func ReadLinesFunc(path string, f func(string) error) error {
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		if err := f(scanner.Text()); err != nil {
			return err
		}
	}
	return nil
}

// ReadLines reads all lines of the file.
func ReadLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	return lines, scanner.Err()
}

func IsTestEnv(env string) bool {
	return env == "pre" || env == "staging" || env == "test"
}

func ReadArrayLine(fn string) []string {
	bs, err := os.ReadFile(fn)
	if err != nil {
		fmt.Println("ReadArrayLine", err)
		panic(err)
	}
	return strings.Split(strings.TrimSpace(string(bs)), ",")
}

func ReadJSONFileAsStringMap(fn string) map[string]string {
	bs, err := os.ReadFile(fn)
	if err != nil {
		fmt.Println("ReadJSONFileAsStringMap", err)
		panic(err)
	}
	v := make(map[string]string)
	if err = json.Unmarshal(bs, &v); err != nil {
		fmt.Println("ReadJSONFileAsStringMap", err)
		panic(err)
	}
	return v
}
func ReadJSONFileUsingGjson(fn string) gjson.Result {
	bs, err := os.ReadFile(fn)
	if err != nil {
		fmt.Println("ReadJSONFileUsingGjson", err)
		panic(err)
	}
	return gjson.ParseBytes(bs)
}
func ReadJSONArrayFile[T any](fn string) []T {
	bs, err := os.ReadFile(fn)
	if err != nil {
		fmt.Println("ReadJSONArrayFile", err)
		panic(err)
	}
	var res []T
	if err = sonic.Unmarshal(bs, &res); err != nil {
		fmt.Println("ReadJSONArrayFile", err)
		panic(err)
	}
	return res
}
func ReadFile(fn string) []byte {
	bs, err := os.ReadFile(fn)
	if err != nil {
		fmt.Println("ReadFile", err)
		return nil
	}
	return bs
}
func WriteFile(fn string, in []byte) error {
	f, err := os.Create(fn)
	if err != nil {
		panic(err)
	}
	if _, err := f.Write(in); err != nil {
		return err
	}
	return f.Close()
}
func WriteString2File(fn string, in string) error {
	f, err := os.Create(fn)
	if err != nil {
		panic(err)
	}
	if _, err := f.WriteString(in); err != nil {
		return err
	}
	return f.Close()
}

func getFieldByJsonTag(val reflect.Value, tagName string) reflect.Value {
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)
		tag := field.Tag.Get("json")
		if tag == "" {
			continue
		}
		// Handle cases where tag might have options like "omitempty"
		if commaIdx := strings.Index(tag, ","); commaIdx != -1 {
			tag = tag[:commaIdx]
		}
		if tag == tagName {
			return val.Field(i)
		}
	}
	return reflect.Value{}
}

func WriteStructsToCSV(a []interface{}, headers []string, fn string) error {
	file, err := os.Create(fn)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write the header
	if err = writer.Write(headers); err != nil {
		return err
	}

	// Write the data
	for _, v := range a {
		val := reflect.ValueOf(v)
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}

		if val.Kind() != reflect.Struct {
			return fmt.Errorf("expected struct or pointer to struct, got %T", v)
		}

		row := make([]string, len(headers))
		for i, h := range headers {
			fieldFound := false
			// Iterate through all struct fields
			for j := 0; j < val.NumField(); j++ {
				field := val.Type().Field(j)
				// Match header with struct field name or tag
				if field.Name == h || field.Tag.Get("json") == h {
					fVal := val.Field(j)
					if fVal.CanInterface() {
						row[i] = cast.ToString(fVal.Interface())
						fieldFound = true
						break
					}
				}
			}
			if !fieldFound {
				row[i] = ""
			}
		}
		if err = writer.Write(row); err != nil {
			return err
		}
	}
	return nil
}

// WriteLines writes each string in the slice to a file, one per line.
// It takes a filename and a slice of strings as input.
// If an error occurs during file opening or writing, it will return the error.
func WriteLines(filename string, lines []string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	for _, line := range lines {
		_, err := writer.WriteString(line)
		if err != nil {
			return err
		}
		_, err = writer.WriteString("\n")
		if err != nil {
			return err
		}
	}
	return writer.Flush()
}

// WriteCSV writes each string in the slice to a file, one per line.
// It takes a filename and a slice of strings as input.
// If an error occurs during file opening or writing, it will return the error.
func WriteCSV(filename string, lines []map[string]string, ordered ...string) error {
	dstData := make([][]string, 0)
	dstData = append(dstData, ordered)
	for _, line := range lines {
		var row []string
		for _, k := range ordered {
			row = append(row, line[k])
		}
		dstData = append(dstData, row)
	}
	f, err := os.Create(filename)
	if err != nil {
		panic(err)
	}
	w := csv.NewWriter(f)
	if err := w.WriteAll(dstData); err != nil {
		return err
	}
	if err := f.Close(); err != nil {
		return err
	}
	return nil
}
func WriteRawCSV(filename string, lines [][]string) error {
	f, err := os.Create(filename)
	if err != nil {
		panic(err)
	}
	w := csv.NewWriter(f)
	if err := w.WriteAll(lines); err != nil {
		return err
	}
	if err := f.Close(); err != nil {
		return err
	}
	return nil
}

// ProcessCSVFile 处理CSV文件（保持向后兼容）
func ProcessCSVFile(fn string, p func(line int, m map[string]string) bool) {
	ProcessDelimitedFile(fn, ',', p)
}

// ProcessTSVFile 处理TSV文件
func ProcessTSVFile(fn string, p func(line int, m map[string]string) bool) {
	ProcessDelimitedFile(fn, '\t', p)
}

// ProcessDelimitedFile 处理分隔符文件（CSV/TSV等）
func ProcessDelimitedFile(fn string, delimiter rune, p func(line int, m map[string]string) bool) {
	f, err := os.OpenFile(fn, os.O_RDWR, 064)
	if err != nil {
		panic(fmt.Sprintf("ProcessDelimitedFile %v", err))
	}
	defer f.Close()
	DelimitedToMap(f, delimiter, p)
}

// ReadCSVFile 读取CSV文件（保持向后兼容）
func ReadCSVFile(fn string) []map[string]string {
	return ReadDelimitedFile(fn, ',')
}

// ReadTSVFile 读取TSV文件
func ReadTSVFile(fn string) []map[string]string {
	return ReadDelimitedFile(fn, '\t')
}

// ReadDelimitedFile 读取分隔符文件
func ReadDelimitedFile(fn string, delimiter rune) []map[string]string {
	f, err := os.OpenFile(fn, os.O_RDWR, 064)
	if err != nil {
		panic(fmt.Sprintf("ReadDelimitedFile %v", err))
	}
	defer f.Close()
	return DelimitedToMap(f, delimiter, nil)
}

// DelimitedToMap 将分隔符文件转换为map数组，使用第一行作为键
func DelimitedToMap(reader io.Reader, delimiter rune, p func(line int, m map[string]string) bool) []map[string]string {
	r := csv.NewReader(reader)
	r.Comma = delimiter
	r.LazyQuotes = true
	var rows []map[string]string
	var header []string
	var line int
	for {
		line++
		record, err := r.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			panic(fmt.Sprintf("DelimitedToMap %v", err))
		}
		if header == nil {
			for _, k := range record {
				if kk := []rune(k); len(kk) > 0 && kk[0] == '\uFEFF' { // remove bom
					k = string([]rune(k)[1:])
				}
				header = append(header, strings.Trim(strings.TrimSpace(k), `"`))
			}
		} else {
			dict := map[string]string{}
			for i := range header {
				if i < len(record) {
					if _, ok := dict[header[i]]; !ok {
						dict[header[i]] = strings.TrimSpace(record[i])
					}
				}
			}
			if p != nil {
				if !p(line, dict) {
					return nil
				}
				continue
			}
			rows = append(rows, dict)
		}
	}
	return rows
}

// CSVToMap takes a reader and returns an array of dictionaries, using the header row as the keys
// 保持向后兼容，内部调用 DelimitedToMap
func CSVToMap(reader io.Reader, p func(line int, m map[string]string) bool) []map[string]string {
	return DelimitedToMap(reader, ',', p)
}

func MapToUpdateFields(m map[string]string, intFields []string) string {
	sr := []string{}
	for k, v := range m {
		sb := strings.Builder{}
		sb.WriteString(fmt.Sprintf("`%s`=", k))
		if slices.Contains(intFields, k) {
			sb.WriteString(cast.ToString(v))
		} else {
			sb.WriteString("'")
			sb.WriteString(v)
			sb.WriteString("'")
		}
		sr = append(sr, sb.String())
	}
	return strings.Join(sr, ",")
}

type FileFinder struct {
	directory string
	pattern   *regexp.Regexp
}

func NewFileFinder(directory string, pattern string) *FileFinder {
	re := regexp.MustCompile(pattern)
	return &FileFinder{
		directory: directory,
		pattern:   re,
	}
}

func (ff *FileFinder) FindMaxNumberedFile() string {
	maxNumber := -1
	maxFileName := ""
	entries, err := os.ReadDir(ff.directory)
	if err != nil {
		fmt.Println(err)
		return ""
	}
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}
		if ff.pattern.MatchString(entry.Name()) {
			submatches := ff.pattern.FindStringSubmatch(entry.Name())
			if len(submatches) > 0 {
				fileStr := submatches[0]
				lastUnderscoreIndex := 0
				for i, char := range fileStr {
					if char == '_' {
						lastUnderscoreIndex = i
					}
				}
				extIndex := len(fileStr)
				for i := len(fileStr) - 1; i >= 0; i-- {
					if fileStr[i] == '.' {
						extIndex = i
						break
					}
				}
				numStr := fileStr[lastUnderscoreIndex+1 : extIndex]
				num, err := strconv.Atoi(numStr)
				if err != nil {
					fmt.Println(err)
					continue
				}
				if num > maxNumber {
					maxNumber = num
					maxFileName = filepath.Join(ff.directory, entry.Name())
				}
			}
		}
	}
	if maxNumber == -1 {
		return ""
	}
	return maxFileName
}

// GetJSONTags returns the json tag names from a struct
func GetJSONTags(s interface{}) []string {
	t := reflect.TypeOf(s)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return nil
	}

	var tags []string
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := field.Tag.Get("json")
		if tag != "" && tag != "-" {
			// Handle cases where tag might have options like "omitempty"
			if commaIdx := strings.Index(tag, ","); commaIdx != -1 {
				tag = tag[:commaIdx]
			}
			tags = append(tags, tag)
		}
	}
	return tags
}

// DetectFileFormat 根据文件扩展名自动检测文件格式
func DetectFileFormat(fn string) (delimiter rune, err error) {
	ext := strings.ToLower(filepath.Ext(fn))
	switch ext {
	case ".tsv":
		return '\t', nil
	case ".csv":
		return ',', nil
	case ".psv":
		return '|', nil
	case ".ssv":
		return ';', nil
	default:
		// 如果没有扩展名或未知扩展名，默认使用逗号
		return ',', nil
	}
}

// ProcessFileAutoDetect 自动检测文件格式并处理
func ProcessFileAutoDetect(fn string, p func(line int, m map[string]string) bool) error {
	delimiter, err := DetectFileFormat(fn)
	if err != nil {
		return fmt.Errorf("detect file format failed: %w", err)
	}

	ProcessDelimitedFile(fn, delimiter, p)
	return nil
}

// ReadFileAutoDetect 自动检测文件格式并读取
func ReadFileAutoDetect(fn string) ([]map[string]string, error) {
	delimiter, err := DetectFileFormat(fn)
	if err != nil {
		return nil, fmt.Errorf("detect file format failed: %w", err)
	}

	return ReadDelimitedFile(fn, delimiter), nil
}
