package protocol

import (
	"hotel/common/bff"
	"hotel/common/pagehelper"
	"hotel/common/utils"
	"hotel/user/domain"
)

type AddAuditLogReq struct {
	Log      *domain.AuditLog
	Operator *domain.User `json:"operator" apidoc:"-"`
}

type AddAuditLogResp struct {
}

type ListAuditLogReq struct {
	ActionType string           `json:"actionType"`
	ActionTime utils.TimeWindow `json:"actionTime"`
	pagehelper.PageReq
}
type ListAuditLogResp struct {
	bff.Table[domain.AuditLog]
}
