package metrics

import (
	"context"
	"fmt"
	"math"
	"sync"

	"github.com/prometheus/client_golang/prometheus"

	"hotel/common/log"
)

var (
	APICallTiming      = New().WithTimer(apiCallTiming, []string{serviceKey, methodKey, bizName<PERSON>ey, customerNameKey})
	APICallCount       = New().WithTimer(apiCallCount, []string{serviceKey, methodKey, bizNameKey, customerNameKey})
	APICallBizErrCount = New().WithTimer(apiCallCountBizErrCount, []string{serviceKey, methodKey, bizNameKey, customerNameKey, codeKey})
	InfraCallTiming    = New().WithTimer(infraCallTiming, []string{nameKey})
	// businessCallTiming for business call timing
	BusinessCallTiming   = New().WithTimer(businessCallTiming, []string{nameKey})
	BusinessDistribution = New().WithTimer(businessDistribution, []string{nameKey})
	// businessCodeCount for business code count
	BusinessCodeCount = New().WithCounter(businessCodeCount, []string{nameKey, codeKey})
	// businessEnumCount for business enum count
	BusinessEnumCount = New().WithCounter(businessEnumCount, []string{nameKey, enumKey})
	// businessMethodTiming for business method timing
	BusinessMethodTiming = New().WithTimer(businessMethodTiming, []string{nameKey, methodKey})
	// businessMethodCount for business method count
	BusinessMethodCount    = New().WithCounter(businessMethodCount, []string{nameKey, methodKey})
	BusinessMethodErrCount = New().WithCounter(businessMethodErrCount, []string{nameKey, methodKey})
	// businessErrCount for business err count
	BusinessErrCount = New().WithCounter(businessErrCount, []string{nameKey})
	// businessInfoCount for business info count
	BusinessInfoCount = New().WithCounter(businessInfoCount, []string{nameKey})
	// CacheHit for cache hit
	CacheHit = New().WithCounter(cacheHit, []string{nameKey})
	// CacheMiss for cache miss
	CacheMiss = New().WithCounter(cacheMiss, []string{nameKey})
	// BusinessHit for cache hit
	BusinessHit = New().WithCounter(businessHit, []string{nameKey})
	// BusinessMiss for cache miss
	BusinessMiss = New().WithCounter(businessMiss, []string{nameKey})
)

func New() *Prom {
	return &Prom{}
}

type Prom struct {
	histogram *prometheus.HistogramVec
	summary   *prometheus.SummaryVec
	counter   *prometheus.GaugeVec
	state     *prometheus.GaugeVec
	once      sync.Once

	name     string
	tags     []string
	format   string        // transient
	args     []interface{} // transient
	logErr   error         // transient
	logLevel log.Level     // transient
}

func (p *Prom) copy() *Prom {
	return &Prom{name: p.name, tags: p.tags} // copy
}
func (p *Prom) setNameAndTags(name string, tags []string) *Prom {
	p.name = name
	p.tags = tags
	return p
}

func (p *Prom) WithLogf(format string, args ...interface{}) *Prom {
	newp := p.copy()
	newp.format = format
	newp.args = args
	return newp
}

func (p *Prom) WithErrLogf(err error, format string, args ...interface{}) *Prom {
	newp := p.copy()
	newp.format = format
	newp.args = args
	if err != nil {
		newp.logErr = err
		newp.logLevel = log.LevelError
	}
	return newp
}

func (p *Prom) tryLog(ctx context.Context, metricVal interface{}, extra ...string) {
	if p.format == "" {
		return
	}
	kvs := make(map[string]string)
	for i := range p.tags {
		if i >= len(extra) {
			break
		}
		kvs[p.tags[i]] = extra[i]
	}
	format := fmt.Sprintf("name(%s) tags(%v) metric(%v) %s", p.name, kvs, metricVal, p.format)
	switch p.logLevel {
	case log.LevelError:
		log.Errorc(ctx, format, p.args...)
	default:
		log.Infoc(ctx, format, p.args...)
	}
}

func (p *Prom) State(ctx context.Context, name string, val float64, label ...string) {
	label = append([]string{name}, label...)
	p.tryLog(ctx, val, label...)
	if p.state != nil {
		p.state.WithLabelValues(label...).Set(val)
	}
}

func (p *Prom) IncrN(ctx context.Context, n int64, label ...string) {
	p.tryLog(ctx, n, label...)
	if p.counter != nil {
		p.counter.WithLabelValues(label...).Inc()
	}
}

func (p *Prom) Incr(ctx context.Context, label ...string) {
	p.IncrN(ctx, 1, label...)
}

// Timing log timing information (in milliseconds) without sampling
func (p *Prom) Timing(ctx context.Context, time int64, label ...string) {
	if p.format == "" {
		p = p.WithLogf(TimingLogFormat(time))
	}
	p.tryLog(ctx, time, label...)

	p.once.Do(func() {
		if _useSummary {
			if p.summary != nil {
				prometheus.MustRegister(p.summary)
			}
		} else {
			if p.histogram != nil {
				prometheus.MustRegister(p.histogram)
			}
		}
	})
	if _useSummary {
		if p.summary != nil {
			p.summary.WithLabelValues(label...).Observe(float64(time))
		}
	} else {
		if p.histogram != nil {
			p.histogram.WithLabelValues(label...).Observe(float64(time))
		}
	}
}

// WithTimer with summary timer
func (p *Prom) WithTimer(name string, labels []string) *Prom {
	if p == nil {
		return p
	}
	p.setNameAndTags(name, labels)
	if p.histogram == nil {
		p.histogram = prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    name,
				Help:    name,
				Buckets: timingDistributionsFloat64,
			}, labels)
	}
	if p.summary == nil {
		p.summary = prometheus.NewSummaryVec(
			prometheus.SummaryOpts{
				Name:       name,
				Help:       name,
				Objectives: map[float64]float64{0.99: 0.001, 0.9: 0.01},
			}, labels)
	}
	return p
}

// WithCounter sets counter.
func (p *Prom) WithCounter(name string, labels []string) *Prom {
	if p == nil || p.counter != nil {
		return p
	}
	p.setNameAndTags(name, labels)
	p.counter = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: name,
			Help: name,
		}, labels)
	prometheus.MustRegister(p.counter)
	return p
}

// WithState sets state.
func (p *Prom) WithState(name string, labels []string) *Prom {
	if p == nil || p.state != nil {
		return p
	}
	p.setNameAndTags(name, labels)
	p.state = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: name,
			Help: name,
		}, labels)
	prometheus.MustRegister(p.state)
	return p
}

// Decr decrements one stat counter without sampling
func (p *Prom) Decr(ctx context.Context, name string, extra ...string) {
	if p.counter != nil {
		label := append([]string{name}, extra...)
		p.counter.WithLabelValues(label...).Dec()
	}
}

// Add add count  v must > 0
func (p *Prom) Add(ctx context.Context, name string, v int64, extra ...string) {
	label := append([]string{name}, extra...)
	if p.counter != nil {
		p.counter.WithLabelValues(label...).Add(float64(v))
	}
}

func TimingLogFormat(ms int64) string {
	if ms <= 0 { // no need to log
		return ""
	}
	for _, v := range timingDistributions {
		if ms <= v {
			return fmt.Sprintf("%dms (%dms)", v, ms)
		}
	}
	return ""
}

var (
	timingDistributions        = []int64{1, 5, 10, 50, 100, 200, 300, 500, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 15000, 20000, 25000, 30000, 40000, 50000, math.MaxInt64}
	timingDistributionsFloat64 = []float64{1, 5, 10, 50, 100, 200, 300, 500, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 15000, 20000, 25000, 30000, 40000, 50000, math.MaxInt64}
)

const (
	nameKey         = "name"
	codeKey         = "code"
	enumKey         = "enum"
	serviceKey      = "service"
	methodKey       = "method"
	bizNameKey      = "businessName"
	customerNameKey = "customerName"
)

const (
	businessInfoCount       = "go_business_info_count"
	businessErrCount        = "go_business_err_count"
	businessCodeCount       = "go_business_code_count"
	businessEnumCount       = "go_business_enum_count"
	businessMethodCount     = "go_business_method_count"
	businessMethodTiming    = "go_business_method_timing"
	businessMethodErrCount  = "go_business_method_err_count"
	businessCallTiming      = "go_business_call_timing"
	businessDistribution    = "go_business_distribution"
	infraCallTiming         = "go_infra_call_timing"
	businessHit             = "go_business_hit"
	businessMiss            = "go_business_miss"
	cacheHit                = "go_cache_hit"
	cacheMiss               = "go_cache_miss"
	apiCallTiming           = "api_call_timing"
	apiCallCount            = "api_call_count"
	apiCallCountBizErrCount = "api_call_biz_err_count"
)

var (
	// UseSummary use summary for Objectives that defines the quantile rank estimates.
	_useSummary bool
)

//func init() {
//	addFlag(flag.CommandLine)
//}
//
//func addFlag(fs *flag.FlagSet) {
//	v := os.Getenv("PROM_SUMMARY")
//	if v == "true" {
//		_useSummary = true
//	}
//	fs.BoolVar(&_useSummary, "prom_summary", _useSummary, "use summary in prometheus")
//}
