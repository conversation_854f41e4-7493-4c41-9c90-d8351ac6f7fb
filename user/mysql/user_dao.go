package mysql

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"github.com/Danceiny/sentinel-golang/core/flow"
	"github.com/hashicorp/go-set/v3"
	pkgerr "github.com/pkg/errors"

	commonDomain "hotel/common/domain"
	"hotel/common/types"
	"hotel/common/utils"
	"hotel/user/domain"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type UserDao struct {
	cache            *UserCache
	User             *UserModel
	UserRole         *UserRoleModel
	Role             *RoleModel
	Entity           *EntityModel
	EntityUserLink   *EntityUserLinkModel
	EntityEntityLink *EntityEntityLinkModel
}

func newUserDao(cache *UserCache, user *UserModel, userRole *UserRoleModel, role *RoleModel, entity *EntityModel,
	entityUserLinkModel *EntityUserLinkModel, entityEntityLinkModel *EntityEntityLinkModel) *UserDao {
	return &UserDao{cache: cache, User: user, UserRole: userRole, Role: role, Entity: entity,
		EntityUserLink: entityUserLinkModel, EntityEntityLink: entityEntityLinkModel}
}
func (d *UserDao) TxAssignUserRole(ctx context.Context, tx sqlx.Session, userID, entityID types.ID, role *domain.UserRole) error {
	v, err := d.UserRole.withSession(tx).FindOneByUserIdEntityIdRoleId(ctx, userID.Int64(), entityID.Int64(), role.ID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	if v != nil {
		err = d.UserRole.withSession(tx).Update(ctx, convertModelUserRole(userID.Int64(), entityID.Int64(), role))
	} else {
		_, err = d.UserRole.withSession(tx).Insert(ctx, convertModelUserRole(userID.Int64(), entityID.Int64(), role))
	}
	return err
}

func (d *UserDao) TxUpdateUser(ctx context.Context, tx sqlx.Session, user *domain.UserBasic) error {
	err := d.User.withSession(tx).Update(ctx, convertModelUser(user))
	if err != nil {
		return pkgerr.Wrap(err, "TxUpdateUser")
	}
	return nil
}

// UpdateUserBasic 更新用户基本信息
func (d *UserDao) UpdateUserBasic(ctx context.Context, user *domain.UserBasic) error {
	err := d.User.Update(ctx, convertModelUser(user))
	if err != nil {
		return pkgerr.Wrap(err, "UpdateUserBasic")
	}
	return nil
}

// UpdateUserRoles 更新用户角色状态
func (d *UserDao) UpdateUserRoles(ctx context.Context, userID int64, roles []domain.UpdateUserRole) error {
	// 转换为DAO模型
	var userRoles []*UserRole
	for _, role := range roles {
		userRole := &UserRole{
			UserId:   userID,
			RoleId:   role.ID,
			EntityId: role.EntityId,
			Status:   uint64(role.Status),
		}
		userRoles = append(userRoles, userRole)
	}

	// 使用现有的InsertOrUpdateUserRoles方法
	_, err := d.InsertOrUpdateUserRoles(ctx, userRoles)
	return err
}

func convertModelUserRole(userID, entityID int64, role *domain.UserRole) *UserRole {
	return &UserRole{
		UserId:   userID,
		RoleId:   role.ID,
		EntityId: entityID,
		Status:   uint64(role.Status),
	}
}
func (d *UserDao) TxCreateUserBasic(ctx context.Context, tx sqlx.Session, user *domain.UserBasic) error {
	_, err := d.User.withSession(tx).Insert(ctx, convertModelUser(user))
	if err != nil {
		return pkgerr.Wrap(err, "TxCreateUserBasic")
	}
	return nil
}

func (d *UserDao) TxUpsertUser(ctx context.Context, tx sqlx.Session, user *domain.UserBasic) error {
	v, err := d.User.withSession(tx).FindOneByKey(ctx, user.Key)
	if err != nil && !errors.Is(err, ErrNotFound) {
		return pkgerr.Wrap(err, "FindOneByKey")
	}
	if v == nil {
		_, err := d.User.withSession(tx).Insert(ctx, convertModelUser(user))
		if err != nil {
			return pkgerr.Wrapf(err, "InsertUser(%#v)", user)
		}
		return nil
	}
	u := convertModelUser(user)
	return d.User.withSession(tx).Update(ctx, u)
}

func convertModelUser(user *domain.UserBasic) (u *User) {
	defer func() {
		if u.CreateTime.IsZero() {
			u.CreateTime = time.Now()
		}
		if u.UpdateTime.IsZero() {
			u.UpdateTime = time.Now()
		}
	}()
	return &User{
		Id:         user.ID.Int64(),
		Username:   user.Username,
		Key:        user.Key,
		Secret:     user.Secret,
		Profile:    utils.ToJSON(user.Profile),
		CreateTime: user.CreateTime,
	}
}
func (d *UserDao) GetUserByKey(ctx context.Context, key string) (u *domain.User, err error) {
	u, _ = d.cache.GetUserByKey(ctx, key)
	if u != nil {
		return u, nil
	}

	user, err := d.User.FindOneByKey(ctx, key)
	if err != nil {
		return nil, err
	}
	defer func() {
		if u != nil {
			_ = d.cache.SetUser(ctx, u.ID, u)
		}
	}()
	return d.GetUser(ctx, types.ID(user.Id))
}

func (d *UserDao) GetUserByUsername(ctx context.Context, username string) (*domain.User, error) {
	user, err := d.User.FindOneByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	return d.GetUser(ctx, types.ID(user.Id))
}

// GetUserByEntityIDs 注意这个 User 可能是不完整的，因为限定了 Entity
func (d *UserDao) GetUserByEntityIDs(ctx context.Context, entityIDs []int64) ([]*domain.User, error) {
	userRoles, err := d.UserRole.FindByEntityIds(ctx, entityIDs)
	if err != nil {
		return nil, err
	}

	cc, err := d.getDomainUserConverterFromUserRoles(ctx, userRoles)
	if err != nil {
		return nil, err
	}

	return cc.ConvertUsers(), nil
}

// GetUserByEntityIDsAndStatus 注意这个 User 可能是不完整的，因为限定了 entity 以及 是否激活
func (d *UserDao) GetUserByEntityIDsAndStatus(ctx context.Context, entityIDs []int64, activated bool) ([]*domain.User, error) {
	var userRoles []*UserRole
	var err error

	// 如果 entityIDs 为空，查询所有用户角色
	if len(entityIDs) == 0 {
		userRoles, err = d.UserRole.FindAll(ctx)
	} else {
		userRoles, err = d.UserRole.FindByEntityIds(ctx, entityIDs)
	}

	if err != nil {
		return nil, err
	}

	cc, err := d.getDomainUserConverterFromUserRoles(ctx, userRoles)
	if err != nil {
		return nil, err
	}

	return cc.ConvertUsers(), nil
}

func (d *UserDao) InsertOrUpdateUserRoles(ctx context.Context, data []*UserRole) (sql.Result, error) {
	var result sql.Result
	for _, userRole := range data {
		temp, err := d.UserRole.InsertOrUpdate(ctx, userRole)
		if err != nil {
			return temp, err
		}
		result = temp
	}
	return result, nil
}

func (d *UserDao) getDomainUserConverterFromUserRoles(ctx context.Context, userRoles []*UserRole) (*domainUserConverterCtx, error) {
	roleIDs := make([]int64, 0, len(userRoles))
	userIDs := make([]int64, 0, len(userRoles))
	var entityIDs []int64
	var userIdToEntityIDs = make(map[int64]*set.Set[int64])
	for _, v := range userRoles {
		roleIDs = append(roleIDs, v.RoleId)
		userIDs = append(userIDs, v.UserId)
		entityIDs = append(entityIDs, v.EntityId)
		s := userIdToEntityIDs[v.UserId]
		if s == nil {
			userIdToEntityIDs[v.UserId] = set.New[int64](0)
		}
		userIdToEntityIDs[v.UserId].Insert(v.EntityId)
	}
	userBasics, err := d.User.FindByIds(ctx, userIDs)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to find User by ids")
	}
	roles, err := d.Role.FindByIds(ctx, roleIDs)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to find Role by ids")
	}
	entities, err := d.Entity.FindByIds(ctx, entityIDs)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to find Entity by ids")
	}
	entityUserLinksMap := make(map[int64][]*EntityUserLink)
	for userId, entityIds := range userIdToEntityIDs {
		entityUserLinks, err := d.EntityUserLink.FindByUserIdEntityIds(ctx, userId, entityIds.Slice())
		if err != nil {
			return nil, pkgerr.Wrapf(err, "failed to find EntityUserLink by userId(%+v) and entityIds(%#v)", userId, entityIds.Slice())
		}
		entityUserLinksMap[userId] = entityUserLinks
	}

	ancestorsMap := make(map[int64][]*Entity)
	for _, et := range entities {
		ancestors, err := d.Entity.FindByRootId(ctx, et.RootEntityId)
		if err != nil {
			return nil, pkgerr.Wrapf(err, "failed to find Entity by root id %v", et.RootEntityId)
		}
		ancestorsMap[et.Id] = ancestors
	}
	return &domainUserConverterCtx{
		entities:           entities,
		userRoles:          userRoles,
		roles:              roles,
		ancestorsMap:       ancestorsMap,
		userBasics:         userBasics,
		entityUserLinksMap: entityUserLinksMap,
	}, nil
}

func (d *UserDao) GetUser(ctx context.Context, id types.ID) (*domain.User, error) {
	userRoles, err := d.UserRole.FindByUserId(ctx, id.Int64())
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to find User by id")
	}
	cc, err := d.getDomainUserConverterFromUserRoles(ctx, userRoles)
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to convert User by roles")
	}

	return cc.ConvertUser(), nil
}

func (d *UserDao) GetEntityUserLinks(ctx context.Context, userId types.ID, entityIds types.IDs) ([]*domain.EntityUserLink, error) {
	entityUserLinks, err := d.EntityUserLink.FindByUserIdEntityIds(ctx, userId.Int64(), entityIds.Int64s())
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to find EntityUserLink by id")
	}
	out := make([]*domain.EntityUserLink, 0, len(entityUserLinks))
	for _, entityUserLink := range entityUserLinks {
		out = append(out, convert2DomainEntityUserLink(entityUserLink))
	}
	return out, nil
}

func (d *UserDao) GetEntityUserLink(ctx context.Context, userId types.ID, entityId types.ID) (*domain.EntityUserLink, error) {
	res, err := d.GetEntityUserLinks(ctx, userId, types.IDs{entityId})
	if err != nil {
		return nil, pkgerr.Wrap(err, "failed to find EntityUserLink by id")
	}
	if len(res) == 0 {
		return nil, nil
	}
	return res[0], nil
}

type domainUserConverterCtx struct {
	entities           []*Entity
	userRoles          []*UserRole
	roles              []*Role
	ancestorsMap       map[int64][]*Entity
	userBasics         []*User
	entityUserLinksMap map[int64][]*EntityUserLink
}

func (d *domainUserConverterCtx) GetUserIDs() []int64 {
	userIDs := make([]int64, 0, len(d.userRoles))
	for _, v := range d.userRoles {
		userIDs = append(userIDs, v.UserId)
	}
	return userIDs
}
func (d *domainUserConverterCtx) ConvertUser() *domain.User {
	if len(d.userBasics) == 0 {
		return nil
	}
	return convertDomainUser(d.userBasics[0], d.entities, d.userRoles, d.roles, d.ancestorsMap, d.entityUserLinksMap[d.userRoles[0].UserId])
}
func (d *domainUserConverterCtx) ConvertUsers() []*domain.User {
	out := make([]*domain.User, 0, len(d.userBasics))
	for _, user := range d.userBasics {
		out = append(out, convertDomainUser(user, d.entities, d.userRoles, d.roles, d.ancestorsMap, d.entityUserLinksMap[user.Id]))
	}
	return out
}
func convertDomainUser(user *User, entities []*Entity, userRoles []*UserRole, roles []*Role, ancestorsMap map[int64][]*Entity, entityUserLinks []*EntityUserLink) *domain.User {
	return &domain.User{
		UserBasic:             convert2DomainUserBasic(user),
		UserEntityConnections: buildEntityRoles(entities, userRoles, roles, ancestorsMap, entityUserLinks),
	}
}
func buildEntityRoles(entities []*Entity, userRoles []*UserRole, roles []*Role, ancestorsMap map[int64][]*Entity, entityUserLinksMap []*EntityUserLink) []*domain.UserEntityConnection {
	userRolesMap := make(map[int64][]*UserRole)
	for _, userRole := range userRoles {
		userRolesMap[userRole.EntityId] = append(userRolesMap[userRole.EntityId], userRole)
	}
	rolesMap := make(map[int64]*Role)
	for _, role := range roles {
		rolesMap[role.Id] = role
	}
	linkMap := make(map[int64]*EntityUserLink)
	for _, link := range entityUserLinksMap {
		linkMap[link.EntityId] = link
	}
	out := make([]*domain.UserEntityConnection, 0)
	for _, ent := range entities {
		userRolesByEntity := userRolesMap[ent.Id]
		if len(userRolesByEntity) == 0 {
			continue
		}

		treedEntity := convertDomainEntity(ent, ancestorsMap[ent.Id])
		rs := make([]*domain.UserRole, 0)
		for _, userRole := range userRolesByEntity {
			role := rolesMap[userRole.RoleId]
			if role == nil {
				// Skip user roles that don't have corresponding role data
				continue
			}
			role.Scope = treedEntity.FormatRoleScope(role.Scope)
			rs = append(rs, convertDomainUserRole(userRole, role))
		}
		out = append(out, &domain.UserEntityConnection{
			Entity: treedEntity,
			Roles:  rs,
			Link:   convert2DomainEntityUserLink(linkMap[ent.Id]),
		})
	}
	return out
}

func convert2DomainEntityUserLink(link *EntityUserLink) *domain.EntityUserLink {
	if link == nil {
		return nil
	}
	return &domain.EntityUserLink{
		EntityId:             types.ID(link.EntityId),
		UserId:               types.ID(link.UserId),
		Status:               domain.LinkStatus(link.Status),
		SellerInNodeRuleId:   link.SellerInNodeRuleId,
		SellerOutNodeRuleId:  link.SellerOutNodeRuleId,
		BuyerInNodeRuleId:    link.BuyerInNodeRuleId,
		BuyerOutNodeRuleId:   link.BuyerOutNodeRuleId,
		SellerInCredentialId: link.SellerInCredentialId,
		RateLimit:            utils.FromJSONP[flow.Rule](link.RateLimit),
		Info:                 utils.FromJSON[domain.EntityUserLinkInfo](link.Info),
		CreateTime:           link.CreateTime,
	}
}

func convertDomainUserRole(userRole *UserRole, role *Role) *domain.UserRole {
	return &domain.UserRole{
		Status: commonDomain.Status(userRole.Status),
		Role:   convert2DomainRole(role),
	}
}

func convert2DomainRole(role *Role) *domain.Role {
	if role == nil {
		return nil
	}
	return &domain.Role{
		ID:         role.Id,
		Name:       role.Name,
		Privileges: utils.FromJSON[[]string](role.Privileges),
		Scope:      domain.Scope(role.Scope),
	}
}

func ConvertDomainRoles(roles []*Role) []*domain.Role {
	out := make([]*domain.Role, 0, len(roles))
	for _, role := range roles {
		out = append(out, convert2DomainRole(role))
	}
	return out
}

func convert2DomainUserBasic(user *User) *domain.UserBasic {
	if user == nil {
		return nil
	}
	return &domain.UserBasic{
		ID:         types.ID(user.Id),
		Key:        user.Key,
		Username:   user.Username,
		Secret:     user.Secret,
		Profile:    utils.FromJSONP[domain.UserProfile](user.Profile),
		CreateTime: user.CreateTime,
	}
}

func convert2ModelRole(role *domain.Role) *Role {
	return &Role{
		Name:        role.Name,
		Scope:       string(role.Scope),
		Privileges:  utils.ToJSON(role.Privileges),
		Description: utils.ToJSON(role.Description),
	}
}

func convert2ModelPrivilege(p *domain.Privilege) *Privilege {
	return &Privilege{
		Code:        p.Code,
		Name:        p.Name,
		ParentId:    p.ParentID,
		Scope:       string(p.Scope),
		Description: p.Description,
	}
}
