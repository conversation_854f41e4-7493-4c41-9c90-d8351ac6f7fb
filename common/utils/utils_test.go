package utils

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProcessTSVFile(t *testing.T) {
	// 创建测试 TSV 文件
	testData := "hotel_id\tname\tcity\taddress\tstar\n"
	testData += "1\tTest Hotel 1\tBeijing\tTest Address 1\t5\n"
	testData += "2\tTest Hotel 2\tShanghai\tTest Address 2\t4\n"

	tempFile, err := os.CreateTemp("", "test_*.tsv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 测试 ProcessTSVFile
	var processedRows []map[string]string
	ProcessTSVFile(tempFile.Name(), func(line int, row map[string]string) bool {
		if line > 1 { // 跳过标题行
			processedRows = append(processedRows, row)
		}
		return true
	})

	assert.Len(t, processedRows, 2)
	assert.Equal(t, "1", processedRows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", processedRows[0]["name"])
	assert.Equal(t, "Beijing", processedRows[0]["city"])
	assert.Equal(t, "Test Address 1", processedRows[0]["address"])
	assert.Equal(t, "5", processedRows[0]["star"])

	assert.Equal(t, "2", processedRows[1]["hotel_id"])
	assert.Equal(t, "Test Hotel 2", processedRows[1]["name"])
	assert.Equal(t, "Shanghai", processedRows[1]["city"])
	assert.Equal(t, "Test Address 2", processedRows[1]["address"])
	assert.Equal(t, "4", processedRows[1]["star"])
}

func TestReadTSVFile(t *testing.T) {
	// 创建测试 TSV 文件
	testData := "hotel_id\tname\tcity\taddress\tstar\n"
	testData += "1\tTest Hotel 1\tBeijing\tTest Address 1\t5\n"
	testData += "2\tTest Hotel 2\tShanghai\tTest Address 2\t4\n"

	tempFile, err := os.CreateTemp("", "test_*.tsv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 测试 ReadTSVFile
	rows := ReadTSVFile(tempFile.Name())

	assert.Len(t, rows, 2)
	assert.Equal(t, "1", rows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", rows[0]["name"])
	assert.Equal(t, "Beijing", rows[0]["city"])
	assert.Equal(t, "Test Address 1", rows[0]["address"])
	assert.Equal(t, "5", rows[0]["star"])

	assert.Equal(t, "2", rows[1]["hotel_id"])
	assert.Equal(t, "Test Hotel 2", rows[1]["name"])
	assert.Equal(t, "Shanghai", rows[1]["city"])
	assert.Equal(t, "Test Address 2", rows[1]["address"])
	assert.Equal(t, "4", rows[1]["star"])
}

func TestProcessDelimitedFile(t *testing.T) {
	// 测试自定义分隔符（分号）
	testData := "hotel_id;name;city;address;star\n"
	testData += "1;Test Hotel 1;Beijing;Test Address 1;5\n"
	testData += "2;Test Hotel 2;Shanghai;Test Address 2;4\n"

	tempFile, err := os.CreateTemp("", "test_*.txt")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 测试 ProcessDelimitedFile 与分号分隔符
	var processedRows []map[string]string
	ProcessDelimitedFile(tempFile.Name(), ';', func(line int, row map[string]string) bool {
		if line > 1 { // 跳过标题行
			processedRows = append(processedRows, row)
		}
		return true
	})

	assert.Len(t, processedRows, 2)
	assert.Equal(t, "1", processedRows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", processedRows[0]["name"])
	assert.Equal(t, "Beijing", processedRows[0]["city"])
	assert.Equal(t, "Test Address 1", processedRows[0]["address"])
	assert.Equal(t, "5", processedRows[0]["star"])
}

func TestBackwardCompatibility(t *testing.T) {
	// 测试向后兼容性 - CSV 文件应该仍然工作
	testData := "hotel_id,name,city,address,star\n"
	testData += "1,Test Hotel 1,Beijing,Test Address 1,5\n"
	testData += "2,Test Hotel 2,Shanghai,Test Address 2,4\n"

	tempFile, err := os.CreateTemp("", "test_*.csv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 测试 ProcessCSVFile 仍然工作
	var processedRows []map[string]string
	ProcessCSVFile(tempFile.Name(), func(line int, row map[string]string) bool {
		if line > 1 { // 跳过标题行
			processedRows = append(processedRows, row)
		}
		return true
	})

	assert.Len(t, processedRows, 2)
	assert.Equal(t, "1", processedRows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", processedRows[0]["name"])
	assert.Equal(t, "Beijing", processedRows[0]["city"])
	assert.Equal(t, "Test Address 1", processedRows[0]["address"])
	assert.Equal(t, "5", processedRows[0]["star"])

	// 测试 ReadCSVFile 仍然工作
	rows := ReadCSVFile(tempFile.Name())
	assert.Len(t, rows, 2)
	assert.Equal(t, "1", rows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", rows[0]["name"])
}

func TestDetectFileFormatByExtension(t *testing.T) {
	tests := []struct {
		filename  string
		expected  rune
		expectErr bool
	}{
		{"test.csv", ',', false},
		{"test.tsv", '\t', false},
		{"test.psv", '|', false},
		{"test.ssv", ';', false},
		{"test.txt", ',', false}, // 默认使用逗号
		{"test", ',', false},     // 无扩展名，默认使用逗号
		{"test.CSV", ',', false}, // 大小写不敏感
		{"test.TSV", '\t', false},
	}

	for _, tt := range tests {
		t.Run(tt.filename, func(t *testing.T) {
			delimiter, err := DetectFileFormat(tt.filename)
			if tt.expectErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, delimiter)
			}
		})
	}
}

func TestProcessFileAutoDetect(t *testing.T) {
	// 创建测试 TSV 文件
	testData := "hotel_id\tname\tcity\taddress\tstar\n"
	testData += "1\tTest Hotel 1\tBeijing\tTest Address 1\t5\n"
	testData += "2\tTest Hotel 2\tShanghai\tTest Address 2\t4\n"

	tempFile, err := os.CreateTemp("", "test_*.tsv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 测试自动检测处理
	var processedRows []map[string]string
	err = ProcessFileAutoDetect(tempFile.Name(), func(line int, row map[string]string) bool {
		if line > 1 { // 跳过标题行
			processedRows = append(processedRows, row)
		}
		return true
	})

	assert.NoError(t, err)
	assert.Len(t, processedRows, 2)
	assert.Equal(t, "1", processedRows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", processedRows[0]["name"])
	assert.Equal(t, "2", processedRows[1]["hotel_id"])
	assert.Equal(t, "Test Hotel 2", processedRows[1]["name"])
}

func TestReadFileAutoDetect(t *testing.T) {
	// 创建测试 CSV 文件
	testData := "hotel_id,name,city,address,star\n"
	testData += "1,Test Hotel 1,Beijing,Test Address 1,5\n"
	testData += "2,Test Hotel 2,Shanghai,Test Address 2,4\n"

	tempFile, err := os.CreateTemp("", "test_*.csv")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if _, err := tempFile.WriteString(testData); err != nil {
		t.Fatalf("Failed to write test data: %v", err)
	}

	// 测试自动检测读取
	rows, err := ReadFileAutoDetect(tempFile.Name())
	assert.NoError(t, err)
	assert.Len(t, rows, 2)
	assert.Equal(t, "1", rows[0]["hotel_id"])
	assert.Equal(t, "Test Hotel 1", rows[0]["name"])
	assert.Equal(t, "2", rows[1]["hotel_id"])
	assert.Equal(t, "Test Hotel 2", rows[1]["name"])
}
