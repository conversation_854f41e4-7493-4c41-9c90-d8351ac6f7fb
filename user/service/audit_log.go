package service

import (
	"context"
	"hotel/common/bff"
	"hotel/common/pagehelper"
	"hotel/user/domain"

	"hotel/user/protocol"
)

// AddAuditLog
// @desc: 新增审计日志
// @apidoc: -
func (s *UserService) AddAuditLog(ctx context.Context, req *protocol.AddAuditLogReq) error {
	return s.dao.AuditLog.Insert(ctx, req.Log)
}

func (s *UserService) ListAuditLogs(ctx context.Context, req *protocol.ListAuditLogReq) (*protocol.ListAuditLogResp, error) {
	logs, err := s.dao.AuditLog.QueryByActionTypeAndTime(ctx, req.ActionType, req.ActionTime.Start, req.ActionTime.End, req.PageReq)
	if err != nil {
		return nil, err
	}
	return &protocol.ListAuditLogResp{
		Table: bff.Table[domain.AuditLog]{
			PageResp: pagehelper.PageResp{
				HasMore: len(logs) > 0,
			},
			HeaderKeys: []string{
				"actionType",
				"actionTime",
				"actorUserId",
				"actorInfo",
				"affectedEntityId",
				"affectedEntityInfo",
				"details",
			},
			//todo: trae implement it
			Rows:                 nil,
			Header:               nil,
			StyleId:              "",
			IsRowToColumnEnabled: false,
		},
	}, nil
}
