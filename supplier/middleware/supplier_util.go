package middleware

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	pkgerr "github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"hotel/common/bizerr"
	"hotel/common/utils"
	"hotel/supplier/domain"

	"bytes"
	"encoding/xml"

	"github.com/imroc/req/v3"
	"golang.org/x/net/html/charset"
)

type SupplierUtil struct {
	Supplier domain.Supplier
	Config   domain.Config
	HttpCli  *req.Client
	RedisCli *redis.Redis
}

type SupplierUtilWrapper[T any] struct {
	*SupplierUtil
}

func (w *SupplierUtilWrapper[T]) GetProperties(p ...any) (*T, error) {
	if len(p) == 1 {
		if ctx, ok := p[0].(context.Context); ok {
			pl, ok := domain.RetrieveBaseRequestContextPayload(ctx)
			if !ok {
				return nil, domain.Err_MissingBaseRequestContextPayload
			}
			return GetProperties[T](w.Supplier<PERSON>til, pl.Properties)
		}
		return GetProperties[T](w.SupplierUtil, p[0])
	}
	return nil, fmt.Errorf("too many parameters: %#v", p)
}

func (w *SupplierUtilWrapper[T]) Execute(ctx context.Context, apiName string, req interface{}, resp interface{}, opts ...ExecuteOption) error {
	return Execute[T](ctx, w.SupplierUtil, apiName, req, resp, opts...)
}

func SupplierUtilWrapperFromConfigFilePath[T any](supplier domain.Supplier, p string) *SupplierUtilWrapper[T] {
	su := SupplierUtilFromConfigFilePath(supplier, p)
	return &SupplierUtilWrapper[T]{
		SupplierUtil: su,
	}
}

func SupplierUtilFromConfigFilePath(supplier domain.Supplier, p string) *SupplierUtil {
	var cfg domain.Config
	conf.MustLoad(p, &cfg)
	redisClient, err := redis.NewRedis(cfg.Redis)
	if err != nil {
		panic(err)
	}
	return &SupplierUtil{
		Config: cfg,
		HttpCli: req.C().
			SetTimeout(30 * time.Second).
			SetCommonRetryCount(3).
			OnBeforeRequest(RequestLogMiddleware).
			OnAfterResponse(ResponseLogMiddleware),
		RedisCli: redisClient,
		Supplier: supplier,
	}
}

func GetProperties[T any](u *SupplierUtil, p any) (*T, error) {
	if p == nil {
		return nil, bizerr.AuthenticationErr.WithMessage(fmt.Sprintf("empty properties of %s", u.Supplier.String()))
	}
	var t T
	switch pp := p.(type) {
	case string:
		if err := sonic.UnmarshalString(pp, &t); err != nil {
			return nil, pkgerr.Wrapf(err, "failed to unmarshal properties: %s", p)
		}
	case []byte:
		if err := sonic.Unmarshal(pp, &t); err != nil {
			return nil, pkgerr.Wrapf(err, "failed to unmarshal properties: %s", p)
		}
	case *T:
		return pp, nil
	case T:
		return &pp, nil
	case domain.BaseRequestContextPayload:
		return GetProperties[T](u, pp.Properties)
	case *domain.BaseRequestContextPayload:
		return GetProperties[T](u, pp.Properties)
	default:
		ps, err := sonic.Marshal(pp)
		if err != nil {
			return nil, pkgerr.Wrapf(err, "failed to marshal properties: %s", p)
		}
		if err = sonic.Unmarshal(ps, &t); err != nil {
			return nil, pkgerr.Wrapf(err, "failed to unmarshal properties: %s", string(ps))
		}
	}
	return &t, nil
}

type UsernameAndPassword interface {
	GetUsernameAndPassword() (string, string)
}

type BearerToken interface {
	GetBearerToken() string
}

type BearerTokenStruct struct {
	Token string
}

func (b BearerTokenStruct) GetBearerToken() string {
	return b.Token
}

type executeOpt struct {
	auth          interface{}
	errResp       interface{}
	customHeaders map[string]string
	pathVars      map[string]string
}

type ExecuteOption func(*executeOpt)

func WithAuth(auth interface{}) ExecuteOption {
	return func(opt *executeOpt) {
		opt.auth = auth
	}
}
func WithBearerToken(token string) ExecuteOption {
	return func(opt *executeOpt) {
		opt.auth = BearerTokenStruct{Token: token}
	}
}

func WithErrRespType(errResp interface{}) ExecuteOption {
	return func(opt *executeOpt) {
		opt.errResp = errResp
	}
}

func WithCustomHeaders(headers map[string]string) ExecuteOption {
	return func(opt *executeOpt) {
		opt.customHeaders = headers
	}
}

func WithPathVars(vars map[string]string) ExecuteOption {
	return func(opt *executeOpt) {
		if opt.customHeaders == nil {
			opt.customHeaders = make(map[string]string)
		}
		if opt.pathVars == nil {
			opt.pathVars = make(map[string]string)
		}
		for k, v := range vars {
			opt.pathVars[k] = v
		}
	}
}

func Execute[T any](ctx context.Context, u *SupplierUtil, apiName string, req interface{}, resp interface{}, opts ...ExecuteOption) error {
	o := new(executeOpt)
	for _, opt := range opts {
		opt(o)
	}
	pl, ok := domain.RetrieveBaseRequestContextPayload(ctx)
	if !ok {
		return domain.Err_MissingBaseRequestContextPayload
	}

	p, err := GetProperties[T](u, pl.Properties)
	if err != nil {
		return err
	}
	if o.auth == nil {
		o.auth = p
	}

	apiCfg := u.Config.API[apiName]
	if apiCfg.HttpMethod == "" {
		apiCfg.HttpMethod = http.MethodPost
	}
	if apiCfg.EnableEncode {
		if o.customHeaders == nil {
			o.customHeaders = make(map[string]string)
		}
		o.customHeaders["Accept-Encoding"] = "gzip"
	}

	apiUrl := strings.TrimSuffix(apiCfg.BaseURL, "/") + apiCfg.Path
	if o.pathVars != nil {
		// support "https://solo.derbysoft-test.com/bookingusbv4/hotels/{supplierId}"
		apiUrl = strings.ReplaceAll(apiUrl, "{", "${")
		apiUrl = os.Expand(apiUrl, func(key string) string {
			if v, ok := o.pathVars[key]; ok {
				return v
			}
			return "$" + key
		})
	}

	// timeout control
	consumed := time.Since(time.Unix(pl.Header.ServerRequestTimestamp, 0))
	timeout := time.Duration(apiCfg.TimeoutSeconds) * time.Second
	if pl.Header.TimeoutMilliseconds > 0 {
		timeout = time.Duration(pl.Header.TimeoutMilliseconds)*time.Millisecond - consumed
	}
	if timeout <= 0 {
		timeout = 30 * time.Second
	}
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// request builder
	request := u.HttpCli.R().
		SetContext(ctx)

	if o.auth != nil {
		if v, ok := o.auth.(UsernameAndPassword); ok {
			request.SetBasicAuth(v.GetUsernameAndPassword())
		} else if v, ok := o.auth.(BearerToken); ok {
			request.SetHeader("Authorization", "Bearer "+v.GetBearerToken())
		}
	}

	// 设置自定义请求头
	if o.customHeaders != nil {
		for key, value := range o.customHeaders {
			request.SetHeader(key, value)
		}
	}

	// request params or body
	if apiCfg.HttpMethod == http.MethodGet {
		if req != nil {
			queryParams := utils.StructToMapStringString(req, utils.WithSliceAsCommaSeparated())
			if len(queryParams) > 0 {
				request = request.SetQueryParams(queryParams)
			}
		}
	} else {
		// use response header to determine content type
		switch apiCfg.ContentType {
		case "xml":
			request = request.SetBodyXmlMarshal(req)
		default:
			request = request.SetBodyJsonMarshal(req)
		}
	}

	httpResp, err := request.Send(apiCfg.HttpMethod, apiUrl)
	if err != nil {
		return err
	}

	if httpResp.IsErrorState() {
		if o.errResp == nil {
			return bizerr.NewHTTP(-1, fmt.Sprintf("error calling %s: %s", apiName, httpResp.String()), int32(httpResp.GetStatusCode()))
		}
		err = sonic.Unmarshal(httpResp.Bytes(), &o.errResp)
		if err != nil {
			return fmt.Errorf("error unmarshalling error response: %v", err)
		}
		return fmt.Errorf("error calling %s: %s", apiName, httpResp.String())
	}

	// 自动识别Content-Type为xml时用xml解码，否则用sonic/json
	contentType := httpResp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = apiCfg.ContentType
	}
	if resp != nil {
		if isXMLContentType(contentType) {
			decoder := xml.NewDecoder(bytes.NewReader(httpResp.Bytes()))
			decoder.CharsetReader = charset.NewReaderLabel
			return decoder.Decode(resp)
		} else {
			return sonic.Unmarshal(httpResp.Bytes(), resp)
		}
	}
	return nil
}

// 判断Content-Type是否为xml
func isXMLContentType(contentType string) bool {
	if contentType == "" {
		return false
	}
	ct := strings.ToLower(contentType)
	return strings.Contains(ct, "xml")
}
