package domain

import (
	"hotel/common/pagehelper"
	"hotel/common/types"
	geoDomain "hotel/geography/domain"
)

type HotelListReq struct {
	SupplierHotelIds []string `json:"supplierHotelIds"` //optional
	CheckInOut
	GuestRoomOption
	HotelRegion
	HotelListFilter
	Ranking
	pagehelper.PageReq
}

type CheckInOut struct {
	CheckIn  types.DateInt `json:"checkIn" required:"true" default:"now()"`
	CheckOut types.DateInt `json:"checkOut" required:"true" default:"now()+7"`
}

type CountryOption struct {
	// The country code of the booker's point of sale, in ISO 3166-1 alpha-2 format. eg: "US"
	// This should represent the country where the transaction is taking place.
	CountryCode string `json:"countryCode"`
	// The residency code of the booker, in ISO 3166-1 alpha-2 format. eg: "US"
	ResidencyCode string `json:"residencyCode"`
	// The nationality code of the booker, in ISO 3166-1 alpha-2 format. eg: "US"
	NationalityCode string `json:"nationalityCode"`
}

func (c CountryOption) GetNationalityCode() string {
	if c.NationalityCode != "" {
		return c.NationalityCode
	}
	if c.ResidencyCode != "" {
		return c.ResidencyCode
	}
	return c.CountryCode
}

type GuestRoomOption struct {
	CountryOption
	RoomCount     int64   `json:"roomCount"`     // default as 1
	AdultCount    int64   `json:"adultCount"`    // default as 1, per room
	ChildrenCount int64   `json:"childrenCount"` // per room
	Guests        []Guest `json:"guests"`
}

func (o GuestRoomOption) GetRoomCount() int64 {
	if o.RoomCount <= 0 {
		o.RoomCount = 1
	}
	return o.RoomCount
}

type HotelRegion struct {
	RegionName     string `json:"regionName"`     // optional
	SupplierCityId string `json:"supplierCityId"` // optional
}

type Ranking struct {
	Order   SearchSortType
	OrderBy SearchSortName
}
type HotelListFilter struct {
	Distance     *DistanceFilter `json:"distance"`
	Price        *PriceFilter    `json:"price"`
	FreeCancel   *bool           `json:"freeCancel"`
	HasBreakfast *bool           `json:"hasBreakfast"`
}

type HotelListResp struct {
	Hotels []SupplierHotel `json:"hotels"`
	pagehelper.PageResp
}

type DistanceFilter struct {
	geoDomain.Nearby
}

type PriceFilter struct {
	LowPrice  float64 `json:"lowPrice"`
	HighPrice float64 `json:"highPrice"`
}

type KeywordTypeEnum int64

const (
	HOTEL           KeywordTypeEnum = 1
	HOTEL_BRAND     KeywordTypeEnum = 2
	HOTEL_GROUP     KeywordTypeEnum = 3
	CITY            KeywordTypeEnum = 4
	SCENIC_AREA     KeywordTypeEnum = 5
	CORP_PLACE      KeywordTypeEnum = 6
	RAILWAY_STATION KeywordTypeEnum = 7
	LANDMARK        KeywordTypeEnum = 8
	LOCATION        KeywordTypeEnum = 9
	AIRPORT         KeywordTypeEnum = 10
	INTL_AIRPORT    KeywordTypeEnum = 11
	METRO_STATION   KeywordTypeEnum = 12
	ZONE            KeywordTypeEnum = 13
	OTHER           KeywordTypeEnum = 14
)

func (p KeywordTypeEnum) String() string {
	switch p {
	case HOTEL:
		return "HOTEL"
	case HOTEL_BRAND:
		return "HOTEL_BRAND"
	case HOTEL_GROUP:
		return "HOTEL_GROUP"
	case CITY:
		return "CITY"
	case SCENIC_AREA:
		return "SCENIC_AREA"
	case CORP_PLACE:
		return "CORP_PLACE"
	case RAILWAY_STATION:
		return "RAILWAY_STATION"
	case LANDMARK:
		return "LANDMARK"
	case LOCATION:
		return "LOCATION"
	case AIRPORT:
		return "AIRPORT"
	case INTL_AIRPORT:
		return "INTL_AIRPORT"
	case METRO_STATION:
		return "METRO_STATION"
	case ZONE:
		return "ZONE"
	case OTHER:
		return "OTHER"
	default:
		return "<UNSET>"
	}
}
