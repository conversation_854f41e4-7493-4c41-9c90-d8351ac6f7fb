package domain

import (
	"time"

	"hotel/common/money"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

type Order struct {
	Id            types.ID              `json:"id" required:"true"`
	ReferenceNo   string                `json:"referenceNo,omitempty"`
	Status        OrderStatus           `json:"status" required:"true"`
	CreateTime    time.Time             `json:"createTime,omitzero"`
	ConfirmNumber string                `json:"confirmNumber,omitempty"`
	Rooms         []OrderRoom           `json:"orderRoom,omitzero"`
	Booker        supplierDomain.Booker `json:"booker,omitzero"`
	Tags          []string              `json:"tags,omitzero"`
	OrderAccount  *OrderAccount         `json:"orderAccount,omitempty" apidoc:"HotelCode"`
	BizInfo       *OrderBizInfo         `json:"bizInfo,omitempty" apidoc:"HotelCode"`
}

type OrderSummary struct {
	Id             types.ID                      `json:"id" required:"true"`
	ReferenceNo    string                        `json:"referenceNo,omitempty"`
	Status         OrderStatus                   `json:"status" required:"true"`
	CreateTime     time.Time                     `json:"createTime,omitzero"`
	Nights         int64                         `json:"rooms,omitzero"`
	Rooms          int64                         `json:"orderRoom,omitzero"`
	Tags           []string                      `json:"tags,omitzero"`
	SalesMoney     money.Money                   `json:"salesMoney,omitzero"`
	Supplier       supplierDomain.Supplier       `json:"supplier,omitzero"`
	SupplierNet    money.Money                   `json:"supplierNet,omitzero"`
	RefundableMode supplierDomain.RefundableMode `json:"refundableMode,omitzero"`
}

type OrderGainSummary struct {
	Gain                     money.Money `json:"gain,omitzero"`
	Refunded                 money.Money `json:"refunded,omitzero"`
	AvailableRebookingBefore time.Time   `json:"availableRebookingBefore,omitzero"`
	RebookingOrderIdTo       int64       `json:"rebookingOrderIdTo,omitzero"`
	RebookingOrderIdFrom     int64       `json:"rebookingOrderIdFrom,omitzero"`
}

type OrderAccount struct {
	CustomerAccount   *userDomain.UserBasic `json:"customerAccount,omitempty"`
	TenantBrandEntity *userDomain.Entity    `json:"tenantBrandEntity,omitempty"`
	CustomerEntity    *userDomain.Entity    `json:"customerEntity,omitempty"`
}

type OrderRoom struct {
	RoomIndex  int64                   `json:"roomIndex" required:"true"`
	Guests     []*supplierDomain.Guest `json:"guest,omitempty" required:"true"`
	RefundInfo []*OrderRoomRefundInfo  `json:"refundInfo,omitzero"`
}

type OrderRoomRefundInfo struct {
	Date          types.DateInt `json:"date" required:"true"`
	Refunded      bool          `json:"refunded" required:"true"`
	RefundedMoney money.Money   `json:"refundedMoney" required:"true"`
}

type PayOrderReq struct {
	OrderID       int64           `json:"order_id"`
	Currency      string          `json:"currency"`        // 币种
	TotalPrice    int64           `json:"total_price"`     // 用户实际付款金额(总)
	SubOrderPrice map[int64]int64 `json:"sub_order_price"` // 子订单价格
}

type PayOrderResp struct {
	Success bool `json:"success"`
}

type OrderStatus int64

// 订单状态常量
const (
	OrderStateCreated               OrderStatus = 1 // 已创建
	OrderStatePaid                  OrderStatus = 2 // 已支付
	OrderStateNeedSupplierConfirmed OrderStatus = 3 // 等待确认(供应商)
	OrderStateConfirmed             OrderStatus = 4 // 已确认(供应商)
	OrderStateCompleted             OrderStatus = 5 // 已完成入住
	OrderStateCancelled             OrderStatus = 6 // 已取消
	OrderStateNeedCancel            OrderStatus = 7 // 等待取消; rebook 时需要
	OrderStateNeedRefund            OrderStatus = 8 // 等待退款; rebook 时需要
)

func ConvertSupplierOrderStatusToTradeStatus(supplierStatus supplierDomain.OrderStatus) OrderStatus {
	switch supplierStatus {
	case supplierDomain.OrderStatus_Submitted:
		return OrderStatePaid
	case supplierDomain.OrderStatus_Confirming:
		return OrderStateNeedSupplierConfirmed
	case supplierDomain.OrderStatus_Confirmed:
		return OrderStateConfirmed
	case supplierDomain.OrderStatus_Cancelled:
		return OrderStateCancelled
	default:
		return OrderStateCreated
	}
}

func (s OrderStatus) String() string {
	n, _ := StateName(s)
	return n
}

func (s OrderStatus) Int64() int64 {
	return int64(s)
}
