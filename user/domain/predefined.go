package domain

import (
	"os"
	"time"

	commonDomain "hotel/common/domain"
	"hotel/common/i18n"
	geoDomain "hotel/geography/domain"
	supplyDomain "hotel/supplier/domain"
)

var PredefinedCredentialList = []*Credential{
	{
		Supplier:          supplyDomain.Supplier_Dida,
		CredentialId:      1,
		CredentialContent: `{"ClientID": "DidaApiTestID", "LicenseKey": "TestKey"}`,
	},
	{
		Supplier:          supplyDomain.Supplier_Dida,
		CredentialId:      2,
		CredentialContent: `{"ClientID": "TravelToDiscover_Test", "LicenseKey": "vO8sRbn0aMYM93VnNviYFXb7B83Ri3iJ"}`,
	},
}
var PredefinedEntityList = EntityList{
	{
		ID:             1,
		Type:           EntityTypePlatform,
		Name:           "HotelCode",
		RootEntityID:   0,
		ParentEntityID: 0,
		Profile:        EntityProfile{},
		DistributionConfig: DistributionConfig{
			BuyerOutAggregatedRuleId:  0,
			BuyerInAggregatedRuleId:   1,
			SellerOutAggregatedRuleId: 0,
			SellerInAggregatedRuleId:  0,
		},
		FinanceConfig: FinanceConfig{},
		SupplierConfig: SupplierConfig{
			Credentials: []*Credential{
				{
					Supplier:          supplyDomain.Supplier_Dida,
					CredentialId:      1,
					CredentialContent: "",
				},
			},
		},
		Status:     commonDomain.StatusEnable,
		CreateTime: time.Time{},
		Ancestors:  nil,
	},
	{
		ID:                 2,
		Type:               EntityTypeTenant,
		Name:               "ttdbooking",
		RootEntityID:       0,
		ParentEntityID:     0,
		Profile:            EntityProfile{},
		DistributionConfig: DistributionConfig{},
		FinanceConfig:      FinanceConfig{},
		SupplierConfig:     SupplierConfig{},
		Status:             commonDomain.StatusEnable,
		CreateTime:         time.Time{},
		Ancestors:          nil,
	},
	{
		ID:                 3,
		Type:               EntityTypeTenant,
		Name:               "ttdbooking API",
		RootEntityID:       1,
		ParentEntityID:     1,
		Profile:            EntityProfile{},
		DistributionConfig: DistributionConfig{},
		FinanceConfig:      FinanceConfig{},
		SupplierConfig:     SupplierConfig{},
		Status:             commonDomain.StatusEnable,
		CreateTime:         time.Time{},
		Ancestors:          nil,
	},
	{
		ID:                 4,
		Type:               EntityTypeTenant,
		Name:               "ttdbooking.com",
		RootEntityID:       1,
		ParentEntityID:     1,
		Profile:            EntityProfile{},
		DistributionConfig: DistributionConfig{},
		FinanceConfig:      FinanceConfig{},
		SupplierConfig:     SupplierConfig{},
		Status:             commonDomain.StatusEnable,
		CreateTime:         time.Time{},
		Ancestors:          nil,
	},
	{
		ID:             5,
		Type:           EntityTypeCustomer,
		Name:           "alibaba", // benjamin's customer; API forbidden
		RootEntityID:   1,
		ParentEntityID: 3,
		Profile: EntityProfile{
			Country: geoDomain.Country{
				Code: "IR", // iran
			},
		},
		DistributionConfig: DistributionConfig{},
		FinanceConfig:      FinanceConfig{},
		SupplierConfig:     SupplierConfig{},
		Status:             commonDomain.StatusEnable,
		CreateTime:         time.Time{},
		Ancestors:          nil,
	},
	{
		ID:             6,
		Type:           EntityTypeCustomer,
		Name:           "alibaba", // platform's customer; API allowed
		RootEntityID:   0,
		ParentEntityID: 0,
		Profile: EntityProfile{
			Country: geoDomain.Country{
				Code: "IR", // iran
			},
		},
		DistributionConfig: DistributionConfig{},
		FinanceConfig:      FinanceConfig{},
		SupplierConfig:     SupplierConfig{},
		Status:             commonDomain.StatusEnable,
		CreateTime:         time.Time{},
		Ancestors:          nil,
	},
}

var (
	RolePlatformSuperAdmin = &Role{
		ID:   1,
		Name: "platform_super_admin",
		Privileges: []PrivilegeCode{
			PrivilegeCode_InvitePlatformUser,
			PrivilegeCode_InviteTenantUser,
			PrivilegeCode_InviteCustomerUser,
		},
		Scope:       "1:*",
		Description: NewRoleDescription(i18n.New("管理员", "Admin"), i18n.New("平台管理员", "Platform Admin")),
	}
	RolePlatformAdmin = &Role{
		ID:   2,
		Name: "platform_admin",
		Privileges: []PrivilegeCode{
			PrivilegeCode_InviteTenantUser,
			PrivilegeCode_InviteCustomerUser,
		},
		Scope:       "1:*",
		Description: NewRoleDescription(i18n.New("运营", "Operations"), i18n.New("平台运营", "Platform Operations")),
	}
	RolePlatformSupport = &Role{
		ID:          3,
		Name:        "platform_support",
		Privileges:  []PrivilegeCode{},
		Scope:       "1:*",
		Description: NewRoleDescription(i18n.New("只读", "Read-only"), i18n.New("只读", "Platform Readonly")),
	}
	RoleTenantSuperAdmin = &Role{
		ID:   100,
		Name: "tenant_super_admin",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_ManageMarkups,
			PrivilegeCode_TenantUser_SupplierConfiguration,
			PrivilegeCode_TenantUser_CreateBookings,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
			PrivilegeCode_TenantUser_ManageBrands,
		},
		Scope: "1:${tenant_root_entity_id}:*",
		Description: NewRoleDescription(
			i18n.New("管理员", "ParentEntity Admin"),
			i18n.New("完全访问品牌配置、用户、客户和设置", "Full access to brand configuration, users, customers, and settings")),
	}
	RoleTenantAdmin = &Role{
		ID:   101,
		Name: "tenant_admin",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_ManageMarkups,
			PrivilegeCode_TenantUser_SupplierConfiguration,
			PrivilegeCode_TenantUser_CreateBookings,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
			PrivilegeCode_TenantUser_ManageBrands,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("管理员", "ParentEntity Admin"),
			i18n.New("完全访问品牌配置、用户、客户和设置", "Full access to brand configuration, users, customers, and settings")),
	}
	RoleTenantSupplySuperstars = &Role{
		ID:   102,
		Name: "tenant_supply_superstars",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_SupplierConfiguration,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("供应", "Supply"),
			i18n.New("可以管理供应商激活、合同、库存规则", "Can manage supplier activation, contracts, inventory rules")),
	}
	RoleTenantContentMapping = &Role{
		ID:   103,
		Name: "tenant_content_mapping",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_SupplierConfiguration,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("供应", "Supply"),
			i18n.New("可以管理供应商激活、合同、库存规则", "Can manage supplier activation, contracts, inventory rules")),
	}
	RoleTenantSales = &Role{
		ID:   104,
		Name: "tenant_sales",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_CreateBookings,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("销售", "Sales"),
			i18n.New("可以创建/查看预订，访问客户资料", "Can create/view bookings, access customer profiles")),
	}
	RoleTenantOperationsLead = &Role{
		ID:   105,
		Name: "tenant_operations_lead",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_CreateBookings,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
		},
		Scope: "1:${tenant_root_entity_id}:*",
		Description: NewRoleDescription(
			i18n.New("运营", "Operations"),
			i18n.New("可以管理履行、服务请求和预订操作", "Can manage fulfillment, service requests, and booking operations")),
	}

	RoleTenantAgent = &Role{
		ID:   106,
		Name: "tenant_agent",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_SupplierConfiguration,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("代理", "Agent"),
			i18n.New("可以代表客户或直接用户搜索和预订", "Can search and book on behalf of customers or direct users")),
	}
	RoleTenantFinance = &Role{
		ID:   107,
		Name: "tenant_finance",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_ManageMarkups,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("收入", "Revenue"),
			i18n.New("可以管理定价、加价、可用性规则", "Can manage pricing, markups, availability rule")),
	}
	RoleTenantViewOnly = &Role{
		ID:   108,
		Name: "tenant_view_only",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}",
		Description: NewRoleDescription(
			i18n.New("收入", "Revenue"),
			i18n.New("可以管理定价、加价、可用性规则", "Can manage pricing, markups, availability rule")),
	}

	RoleCustomerSuperAdmin = &Role{
		ID:   1000,
		Name: "customer_view_only",
		Privileges: []PrivilegeCode{
			PrivilegeCode_TenantUser_ViewBrandSettings,
			PrivilegeCode_TenantUser_ViewCustomerProfiles,
		},
		Scope: "1:${tenant_root_entity_id}:${tenant_brand_entity_id}:${customer_root_entity_id}",
		Description: NewRoleDescription(
			i18n.New("收入", "Revenue"),
			i18n.New("可以管理定价、加价、可用性规则", "Can manage pricing, markups, availability rule")),
	}

	PredefinedRoleList = RoleList{
		RolePlatformSuperAdmin,
		RolePlatformAdmin,
		RolePlatformSupport,
		RoleTenantSuperAdmin,
		RoleTenantAdmin,
		RoleTenantSales,
		RoleTenantOperationsLead,
		RoleTenantFinance,
		RoleTenantSupplySuperstars,
		RoleTenantAgent,
		RoleTenantViewOnly,
		RoleCustomerSuperAdmin,
	}
)

var PredefinedUsers = []*User{
	{
		UserBasic: &UserBasic{
			ID:         1,
			Key:        "<EMAIL>",
			Username:   "Danceiny",
			Secret:     "hotelbyte.com",
			Profile:    &UserProfile{},
			Activated:  true,
			CreateTime: time.Time{},
		},
		UserEntityConnections: []*UserEntityConnection{
			{
				Entity: PredefinedEntityList.FindByID(1),
				Roles: []*UserRole{
					NewEnabledUserRole(RolePlatformSuperAdmin),
				},
			},
		},
	},
	{
		UserBasic: &UserBasic{
			ID:         2,
			Key:        "<EMAIL>",
			Username:   "Song",
			Secret:     "hotelbyte.com",
			Profile:    &UserProfile{},
			Activated:  true,
			CreateTime: time.Time{},
		},
		UserEntityConnections: []*UserEntityConnection{
			{
				Entity: PredefinedEntityList.FindByID(1),
				Roles: []*UserRole{
					NewEnabledUserRole(RolePlatformAdmin),
				},
			},
		},
	},
	{
		UserBasic: &UserBasic{
			ID:         3,
			Key:        "<EMAIL>",
			Username:   "Benjamin",
			Secret:     "ttdbooking.com",
			Profile:    &UserProfile{},
			Activated:  true,
			CreateTime: time.Time{},
		},
		UserEntityConnections: []*UserEntityConnection{
			{
				Entity: PredefinedEntityList.FindByID(2),
				Roles: []*UserRole{
					NewEnabledUserRole(RoleTenantSuperAdmin),
				},
			},
		},
	},
	{
		UserBasic: &UserBasic{
			ID:         4,
			Key:        "<EMAIL>",
			Username:   "alibaba IRAN",
			Secret:     "alibaba IRAN",
			Profile:    &UserProfile{},
			Activated:  true,
			CreateTime: time.Time{},
		},
		UserEntityConnections: []*UserEntityConnection{
			{
				Entity: PredefinedEntityList.FindByID(5),
				Roles: []*UserRole{
					NewEnabledUserRole(RoleTenantSuperAdmin),
				},
			},
		},
	},
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
