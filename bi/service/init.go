package service

import (
	"context"
	"sync"

	_ "github.com/go-sql-driver/mysql"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/bi/config"
	"hotel/bi/mysql"
	configutils "hotel/common/config"
	"hotel/common/log"
)

var (
	_logSrv       *LogService
	_logOnce      sync.Once
	_biSrv        *OrderService
	_biOnce       sync.Once
	_realtimeSrv  *RealTimeAnalyticsService
	_realtimeOnce sync.Once
)

var configFile = configutils.SafeFlagString("rule", "bi/config/config.yaml", "rule config file")

type LogService struct {
	conn sqlx.SqlConn
	cfg  config.Config
}

func NewLogService() *LogService {
	_logOnce.Do(func() {
		var cfg config.Config
		conf.MustLoad(*configFile, &cfg)
		conn := sqlx.NewMysql(cfg.DSN)
		_logSrv = &LogService{
			conn: conn,
			cfg:  cfg,
		}
		go _logSrv.asyncWriteLog()
	})
	return _logSrv
}

func (s *LogService) Name() string {
	return "bi/log"
}

// OrderService BI订单分析API服务
type OrderService struct {
	analyticsService *OrderAnalyticsService
}

// Name 实现httpdispatcher.Service接口
func (s *OrderService) Name() string {
	return "bi/order"
}

// NewOrderService 从配置创建BI服务
func NewOrderService() *OrderService {
	_biOnce.Do(func() {
		var cfg config.Config
		conf.MustLoad(*configFile, &cfg)
		conn := sqlx.NewMysql(cfg.DSN)

		orderDAO := mysql.NewOrderAnalyticsDAO(conn)
		analyticsService := NewOrderAnalyticsService(orderDAO)
		_biSrv = &OrderService{analyticsService}

		// 启动实时分析服务
		_realtimeOnce.Do(func() {
			_realtimeSrv = NewRealTimeAnalyticsService(orderDAO)
			// 在后台启动实时收集
			go func() {
				ctx := context.Background()
				if err := _realtimeSrv.Start(ctx); err != nil {
					log.Errorc(ctx, "Failed to start real-time analytics service: %v", err)
				}
			}()
		})
	})
	return _biSrv
}

// GetRealTimeAnalyticsService 获取实时分析服务
func GetRealTimeAnalyticsService() *RealTimeAnalyticsService {
	return _realtimeSrv
}
