package service

import (
	"context"
	"testing"

	"hotel/common/types"
	"hotel/trade/protocol"
)

// TestTradeServiceIntegration 集成测试所有 trade service 接口
func TestTradeServiceIntegration(t *testing.T) {
	// 创建服务实例，使用 NewTradeService 保证依赖项初始化
	tradeService := NewTradeService()
	tenantService := &TenantService{TradeService: tradeService}
	customerService := &CustomerService{TradeService: tradeService}

	ctx := context.Background()

	t.Run("OrderHomeFunction", func(t *testing.T) {
		req := &protocol.OrderHomeFunctionReq{}

		// 测试 TenantService
		resp1, err := tenantService.OrderHomeFunction(ctx, req)
		if err != nil {
			t.Errorf("TenantService.OrderHomeFunction() error = %v", err)
		}
		if resp1 == nil {
			t.Errorf("TenantService.OrderHomeFunction() returned nil")
		}

		// 测试 CustomerService
		resp2, err := customerService.OrderHomeFunction(ctx, req)
		if err != nil {
			t.Errorf("CustomerService.OrderHomeFunction() error = %v", err)
		}
		if resp2 == nil {
			t.Errorf("CustomerService.OrderHomeFunction() returned nil")
		}

		// 验证返回数据结构
		if len(resp1.Tags) == 0 {
			t.Errorf("Expected tags, got empty slice")
		}
		if len(resp1.CountryOrderCounters) == 0 {
			t.Errorf("Expected country counters, got empty slice")
		}
	})

	t.Run("ListOrder", func(t *testing.T) {
		req := &protocol.ListOrderReq{
			QueryOrderCriteria: protocol.QueryOrderCriteria{
				PlatformOrderIds: []types.ID{},
			},
		}

		// 测试 TenantService
		resp1, err := tenantService.ListOrder(ctx, req)
		if err != nil {
			t.Errorf("TenantService.ListOrder() error = %v", err)
		}
		if resp1 == nil {
			t.Errorf("TenantService.ListOrder() returned nil")
		}

		// 测试 CustomerService
		resp2, err := customerService.ListOrder(ctx, req)
		if err != nil {
			t.Errorf("CustomerService.ListOrder() error = %v", err)
		}
		if resp2 == nil {
			t.Errorf("CustomerService.ListOrder() returned nil")
		}

		// 验证空结果
		if resp1.Table.Total != 0 {
			t.Errorf("Expected Total = 0, got %d", resp1.Table.Total)
		}
	})

	t.Run("DetailOrder", func(t *testing.T) {
		req := &protocol.DetailOrderReq{
			OrderId: 123,
		}

		// 测试 TradeService
		_, err := tradeService.DetailOrder(ctx, req)
		if err == nil {
			t.Logf("TradeService.DetailOrder() completed (expected error for non-existent order)")
		}

		// 测试 TenantService
		_, err = tenantService.DetailOrder(ctx, req)
		if err == nil {
			t.Logf("TenantService.DetailOrder() completed (expected error for non-existent order)")
		}

		// 测试 CustomerService
		_, err = customerService.DetailOrder(ctx, req)
		if err == nil {
			t.Logf("CustomerService.DetailOrder() completed (expected error for non-existent order)")
		}
	})

	t.Run("QueryOrders", func(t *testing.T) {
		req := &protocol.QueryOrdersReq{
			QueryOrderCriteria: protocol.QueryOrderCriteria{
				PlatformOrderIds: []types.ID{},
			},
		}

		// 测试 TradeService
		resp, err := tradeService.QueryOrders(ctx, req)
		if err != nil {
			t.Errorf("TradeService.QueryOrders() error = %v", err)
		}
		if resp == nil {
			t.Errorf("TradeService.QueryOrders() returned nil")
		}

		// 验证空结果
		if resp.Orders != nil && len(resp.Orders) != 0 {
			t.Errorf("Expected empty orders, got %d", len(resp.Orders))
		}
	})
}

// TestServiceNames 测试服务名称
func TestServiceNames(t *testing.T) {
	tradeService := NewTradeService()
	tenantService := &TenantService{TradeService: tradeService}
	customerService := &CustomerService{TradeService: tradeService}

	if tradeService.Name() != "trade" {
		t.Errorf("Expected TradeService name 'trade', got '%s'", tradeService.Name())
	}

	if tenantService.Name() != "trade/tenant" {
		t.Errorf("Expected TenantService name 'trade/tenant', got '%s'", tenantService.Name())
	}

	if customerService.Name() != "trade/customer" {
		t.Errorf("Expected CustomerService name 'trade/customer', got '%s'", customerService.Name())
	}
}
