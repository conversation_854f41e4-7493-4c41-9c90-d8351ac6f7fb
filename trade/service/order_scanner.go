package service

import (
	"context"
	"encoding/json"
	"time"

	"hotel/trade/domain"

	"hotel/common/log"
)

const (
	scanPaiedOrderTime         = 30 * time.Minute
	scanRefundOrderTime        = 60 * time.Second
	scanPartialRefundOrderTime = 60 * time.Second
	scanCancelOrderTime        = 60 * time.Second
)

func (s *TradeService) startScanOrder() {
	//TODO:使用 Mysql 做到多个微服务只运行一次该方法
	go func() {
		s.startScanPaiedOrder()
		s.startScanCancelOrder()
	}()
}

func (s *TradeService) startScanPaiedOrder() {
	ticker := time.NewTicker(scanPaiedOrderTime)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			paiedOrder, err := s.orderDao.Order.FindList(context.Background(), int64(domain.OrderStatePaid))
			if err != nil {
				log.Error("scanPaiedOrder failed", err)
				continue
			}
			for _, order := range paiedOrder {
				ob, _ := json.Marshal(order)
				// TODO: 重构以适配新的 CQRS 接口
				// 暂时禁用消息发布功能，避免编译错误
				log.Warn("Book message publishing is temporarily disabled due to CQRS refactoring. Order: %+v", order)
				_ = ob // 避免未使用变量警告

				/*
					if sendErr := cqrs.Publish(BookTopic, ob); sendErr != nil {
						log.Error("BookOrderFailed:(%+v),PubOrderError:(%+v)", order, sendErr)
					} else {
						log.Warn("BookOrderPubSuccess:(%+v)", order)
					}
				*/
			}
		}
	}
}

func (s *TradeService) startScanCancelOrder() {
	ticker := time.NewTicker(scanCancelOrderTime)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			cancelOrder, err := s.orderDao.Order.FindList(context.Background(), int64(domain.OrderStateNeedCancel))
			if err != nil {
				log.Error("scanCancelOrder failed", err)
				continue
			}
			for _, order := range cancelOrder {
				// 发布取消消息到CQRS队列
				if sendErr := s.cqrsProducer.Publish(context.Background(), CancelTopic, order); sendErr != nil {
					log.Error("CancelOrderFailed:(%+v),PubOrderError:(%+v)", order, sendErr)
				} else {
					log.Info("CancelOrderPubSuccess:(%+v)", order)
				}
			}
		}
	}
}
